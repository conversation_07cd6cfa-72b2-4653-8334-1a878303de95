# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment variables (CRITICAL - protect Supabase keys)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
.next/
dist-ssr/
*.local

# IDE and editor files
.vscode/*
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~
*.suo
*.ntvs*
*.njsproj*
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Temporary folders
tmp/
temp/

# Archive directory (legacy files)
archive/

# Test files
*.test.html
verification-test.html
surgical-fixes-test.html
.env
