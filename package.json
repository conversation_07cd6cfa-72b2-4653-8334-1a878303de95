{"name": "video-analysis-ui", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@tensorflow/tfjs": "^4.17.0", "@tensorflow/tfjs-backend-webgl": "^4.17.0", "@tensorflow-models/pose-detection": "^2.1.3", "@supabase/supabase-js": "^2.39.7", "framer-motion": "^11.0.8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "lucide-react": "^0.363.0"}, "devDependencies": {"@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.1.6"}}