-- Supabase Storage CORS Policy for Video Files
-- Run this in your Supabase SQL Editor if needed

-- Allow CORS for storage bucket
INSERT INTO storage.cors (bucket_id, allowed_origins, allowed_headers, allowed_methods)
VALUES (
  'videos',
  ARRAY['http://localhost:5173', 'http://127.0.0.1:5173', 'https://your-production-domain.com'],
  ARRAY['authorization', 'x-client-info', 'apikey', 'content-type', 'range'],
  ARRAY['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS']
);

-- Alternative: Update existing CORS policy
UPDATE storage.cors 
SET 
  allowed_origins = ARRAY['http://localhost:5173', 'http://127.0.0.1:5173', 'https://your-production-domain.com'],
  allowed_headers = ARRAY['authorization', 'x-client-info', 'apikey', 'content-type', 'range'],
  allowed_methods = ARRAY['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS']
WHERE bucket_id = 'videos';
