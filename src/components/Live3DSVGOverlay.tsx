/**
 * LIVE SVG SKELETAL OVERLAY COMPONENT
 * 
 * This creates the real-time SVG overlay that appears on top of video elements,
 * matching the reference images from OchyAppOutput.jpeg and other examples.
 * 
 * Features:
 * - Real-time skeletal overlay rendering
 * - Joint markers with confidence-based styling
 * - Angle measurements with arcs and labels
 * - Dynamic scaling and positioning
 * - Performance optimized for smooth playback
 * - Only ONE Live 3D Pose Analyzer instance is created
 */


import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Live3DPoseAnalyzer, LivePoseData, Live3DConfig } from '../utils/Live3DPoseAnalyzer';

interface LiveSVGOverlayProps {
  videoRef: React.RefObject<HTMLVideoElement>;
  config: Live3DConfig;
  isPlaying: boolean;
  onPoseData?: (poseData: LivePoseData | null) => void;
  onAnalysisProgress?: (progress: { 
    currentTime: number; 
    confidence: number; 
    detectionRate: number;
    frameCount: number;
  }) => void;
}

const LiveSVGOverlay: React.FC<LiveSVGOverlayProps> = ({
  videoRef,
  config,
  isPlaying,
  onPoseData,
  onAnalysisProgress
}) => {
  const [currentPose, setCurrentPose] = useState<LivePoseData | null>(null);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const analyzerRef = useRef<Live3DPoseAnalyzer | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const isAnalyzingRef = useRef(false);
  const frameCountRef = useRef(0);
  const detectionCountRef = useRef(0);
  const lastTimeRef = useRef(Date.now());

  // Initialize analyzer ONCE
  useEffect(() => {
    let mounted = true;

    const initializeAnalyzer = async () => {
      try {
        console.log('🎯 Creating Live 3D Pose Analyzer...');
        
        // Create analyzer instance
        const analyzer = new Live3DPoseAnalyzer(config);
        
        // Initialize it
        await analyzer.initialize();
        
        if (!mounted) {
          analyzer.dispose();
          return;
        }

        analyzerRef.current = analyzer;
        setIsReady(true);
        console.log('✅ Live 3D Pose Analyzer ready for analysis');
        
      } catch (err) {
        if (!mounted) return;
        console.error('❌ Failed to initialize analyzer:', err);
        setError(err instanceof Error ? err.message : 'Failed to initialize');
      }
    };

    initializeAnalyzer();

    // Cleanup on unmount ONLY
    return () => {
      mounted = false;
      if (analyzerRef.current) {
        console.log('🧹 Disposing analyzer on unmount');
        analyzerRef.current.dispose();
        analyzerRef.current = null;
      }
    };
  }, []); // Empty deps - initialize ONCE

  // Analyze current frame
  const analyzeFrame = useCallback(async () => {
    if (!analyzerRef.current || !videoRef.current || !isReady) {
      return;
    }

    const video = videoRef.current;
    
    // Check video is ready
    if (video.readyState < 2 || video.paused) {
      return;
    }

    try {
      const poseData = await analyzerRef.current.analyzePose(video);
      frameCountRef.current++;
      
      if (poseData) {
        detectionCountRef.current++;
        setCurrentPose(poseData);
        onPoseData?.(poseData);
        
        // Log every 30 frames
        if (frameCountRef.current % 30 === 0) {
          console.log('🎯 Pose detected:', {
            frame: frameCountRef.current,
            confidence: poseData.detectionConfidence,
            keypoints: Object.keys(poseData.screenCoordinates).length
          });
        }
      } else {
        setCurrentPose(null);
        onPoseData?.(null);
      }

      // Update progress
      if (frameCountRef.current % 10 === 0) {
        const now = Date.now();
        const elapsed = (now - lastTimeRef.current) / 1000;
        const fps = 10 / elapsed;
        lastTimeRef.current = now;

        onAnalysisProgress?.({
          currentTime: video.currentTime,
          confidence: poseData?.detectionConfidence || 0,
          detectionRate: detectionCountRef.current / frameCountRef.current,
          frameCount: frameCountRef.current
        });

        console.log(`📊 Analysis FPS: ${fps.toFixed(1)}, Detection rate: ${((detectionCountRef.current / frameCountRef.current) * 100).toFixed(1)}%`);
      }
    } catch (err) {
      console.error('❌ Frame analysis error:', err);
    }
  }, [isReady, videoRef, onPoseData, onAnalysisProgress]);

  // Animation loop
  useEffect(() => {
    if (!isReady || !isPlaying) {
      isAnalyzingRef.current = false;
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      return;
    }

    console.log('▶️ Starting analysis loop');
    isAnalyzingRef.current = true;

    const loop = () => {
      if (!isAnalyzingRef.current) return;
      
      analyzeFrame();
      animationFrameRef.current = requestAnimationFrame(loop);
    };

    loop();

    return () => {
      console.log('⏸️ Stopping analysis loop');
      isAnalyzingRef.current = false;
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [isReady, isPlaying, analyzeFrame]);

  // Render functions
  const renderConnections = () => {
    if (!currentPose || !analyzerRef.current) return null;

    const connections = analyzerRef.current.getSkeletalConnections();
    const coords = currentPose.screenCoordinates;

    return connections
      .filter(conn => coords[conn.from]?.confidence > 0.3 && coords[conn.to]?.confidence > 0.3)
      .map((conn, idx) => (
        <line
          key={`conn-${idx}`}
          x1={coords[conn.from].x}
          y1={coords[conn.from].y}
          x2={coords[conn.to].x}
          y2={coords[conn.to].y}
          stroke={conn.color}
          strokeWidth={conn.thickness * 0.3}
          opacity={Math.min(coords[conn.from].confidence, coords[conn.to].confidence)}
        />
      ));
  };

  const renderJoints = () => {
    if (!currentPose) return null;

    return Object.entries(currentPose.screenCoordinates)
      .filter(([_, coord]) => coord.confidence > 0.3)
      .map(([name, coord]) => (
        <circle
          key={`joint-${name}`}
          cx={coord.x}
          cy={coord.y}
          r={getJointRadius(name)}
          fill={getJointColor(name)}
          opacity={coord.confidence}
        />
      ));
  };

  const renderAngles = () => {
    if (!currentPose || config.overlayStyle === 'minimal') return null;

    return Object.entries(currentPose.jointAngles)
      .filter(([_, angle]) => angle !== undefined)
      .map(([joint, angle], idx) => {
        const coord = getAnglePosition(joint, currentPose);
        if (!coord) return null;

        return (
          <g key={`angle-${idx}`}>
            <text
              x={coord.x + 5}
              y={coord.y - 5}
              fill="#FF6600"
              fontSize="3"
              fontWeight="bold"
            >
              {Math.round(angle)}°
            </text>
          </g>
        );
      });
  };

  const getJointRadius = (name: string): number => {
    if (name.includes('hip') || name.includes('shoulder')) return 1.5;
    if (name.includes('knee') || name.includes('elbow')) return 1.2;
    return 1;
  };

  const getJointColor = (name: string): string => {
    if (config.overlayStyle === 'minimal') return '#FFFFFF';
    if (name.includes('hip')) return '#FF4444';
    if (name.includes('knee')) return '#FFDD00';
    if (name.includes('ankle')) return '#00FF44';
    if (name.includes('shoulder')) return '#FF8800';
    if (name.includes('elbow')) return '#FFAA00';
    if (name.includes('wrist')) return '#FFCC00';
    return '#88AAFF';
  };

  const getAnglePosition = (joint: string, pose: LivePoseData) => {
    const mapping: { [key: string]: string } = {
      'leftKnee': 'left_knee',
      'rightKnee': 'right_knee',
      'leftElbow': 'left_elbow',
      'rightElbow': 'right_elbow',
      'leftAnkle': 'left_ankle',
      'rightAnkle': 'right_ankle'
    };
    
    const jointName = mapping[joint];
    return jointName ? pose.screenCoordinates[jointName] : null;
  };

  // Error state
  if (error) {
    return (
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-50">
        <div className="bg-red-600 text-white px-4 py-2 rounded">
          Error: {error}
        </div>
      </div>
    );
  }

  // Loading state
  if (!isReady) {
    return (
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-50">
        <div className="bg-blue-600 text-white px-4 py-2 rounded flex items-center">
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
          Initializing 3D Analysis...
        </div>
      </div>
    );
  }

  // Main render
  return (
    <svg
      className="absolute inset-0 w-full h-full pointer-events-none z-40"
      viewBox="0 0 100 100"
      preserveAspectRatio="none"
    >
      <defs>
        <filter id="glow">
          <feGaussianBlur stdDeviation="0.5" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>

      {currentPose ? (
        <g>
          {/* Connections */}
          <g className="connections" filter="url(#glow)">
            {renderConnections()}
          </g>
          
          {/* Joints */}
          <g className="joints">
            {renderJoints()}
          </g>
          
          {/* Angles */}
          <g className="angles">
            {renderAngles()}
          </g>

          {/* Debug info */}
          {config.overlayStyle === 'medical' && (
            <text x="2" y="5" fill="#00FF00" fontSize="2" fontFamily="monospace">
              Scale: {currentPose.realWorldScale.toFixed(2)}x | Conf: {(currentPose.detectionConfidence * 100).toFixed(0)}%
            </text>
          )}
        </g>
      ) : (
        <g>
          <rect x="20" y="45" width="60" height="10" fill="rgba(255,255,0,0.8)" />
          <text x="50" y="52" textAnchor="middle" fill="red" fontSize="4" fontWeight="bold">
            NO POSE DETECTED
          </text>
        </g>
      )}
    </svg>
  );
};

export default LiveSVGOverlay;