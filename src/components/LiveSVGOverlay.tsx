/**
 * LIVE SVG SKELETAL OVERLAY COMPONENT
 * 
 * This creates the real-time SVG overlay that appears on top of video elements,
 * matching the reference images from OchyAppOutput.jpeg and other examples.
 * 
 * Features:
 * - Real-time skeletal overlay rendering
 * - Joint markers with confidence-based styling
 * - Angle measurements with arcs and labels
 * - Dynamic scaling and positioning
 * - Performance optimized for smooth playback
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Live3DPoseAnalyzer, LivePoseData, Live3DConfig, isDebugMode, debugLog } from '../utils/Live3DPoseAnalyzer';
import { checkInitialization } from '../utils/VerificationUtils';

interface LiveSVGOverlayProps {
  videoRef: React.RefObject<HTMLVideoElement>;
  config: Live3DConfig;
  isPlaying: boolean;
  onPoseData?: (poseData: LivePoseData | null) => void;
  onAnalysisProgress?: (progress: { 
    currentTime: number; 
    confidence: number; 
    detectionRate: number;
    frameCount: number;
  }) => void;
}

interface AnalysisStats {
  frameCount: number;
  detectionCount: number;
  totalConfidence: number;
  lastUpdateTime: number;
}

const LiveSVGOverlay: React.FC<LiveSVGOverlayProps> = ({
  videoRef,
  config,
  isPlaying,
  onPoseData,
  onAnalysisProgress
}) => {
  const [currentPose, setCurrentPose] = useState<LivePoseData | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const analyzerRef = useRef<Live3DPoseAnalyzer | null>(null);
  const analysisStatsRef = useRef<AnalysisStats>({
    frameCount: 0,
    detectionCount: 0,
    totalConfidence: 0,
    lastUpdateTime: 0
  });
  const animationFrameRef = useRef<number | null>(null);

  // Initialize analyzer when config changes
  useEffect(() => {
    let mounted = true;
    let initializationComplete = false;

    const initializeAnalyzer = async () => {
      try {
        setError(null);
        setIsAnalyzing(false); // CRITICAL: Keep false during init
        
        console.log('🎯 Initializing Live 3D Pose Analyzer...');
        
        // Dispose previous analyzer if exists
        if (analyzerRef.current) {
          analyzerRef.current.dispose();
          analyzerRef.current = null;
        }

        if (!mounted) return;

        analyzerRef.current = new Live3DPoseAnalyzer(config);
        await analyzerRef.current.initialize();
        
        if (!mounted) {
          analyzerRef.current.dispose();
          return;
        }

        console.log('✅ Live 3D Pose Analyzer ready');
        initializationComplete = true;
        setIsAnalyzing(true); // ONLY set true after complete initialization

        // Run verification check if debug mode is enabled
        if (isDebugMode()) {
          debugLog('Running initialization verification...');
          setTimeout(() => checkInitialization(), 100);
        }
        
      } catch (err) {
        if (!mounted) return;
        
        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize analyzer';
        console.error('❌ Analyzer initialization failed:', err);
        setError(errorMessage);
        setIsAnalyzing(false);
      }
    };

    initializeAnalyzer();

    return () => {
      mounted = false;
      if (analyzerRef.current && !initializationComplete) {
        // Only dispose if initialization wasn't completed
        analyzerRef.current.dispose();
        analyzerRef.current = null;
      }
    };
  }, [config]);

  // Real-time pose analysis loop
  const analyzeCurrentFrame = useCallback(async () => {
    if (!analyzerRef.current || !videoRef.current || !isPlaying) {
      return;
    }

    const video = videoRef.current;

    if (video.readyState < 2) {
      return;
    }

    try {
      const timestamp = video.currentTime;
      console.log(`🎬 LiveSVGOverlay analyzing frame at ${timestamp.toFixed(3)}s`);

      const poseData = await analyzerRef.current.analyzePose(video);

      if (poseData) {
        console.log('✅ LiveSVGOverlay pose detected:', {
          timestamp: poseData.timestamp,
          landmarks: Object.keys(poseData.worldLandmarks).length,
          screenCoords: Object.keys(poseData.screenCoordinates).length,
          confidence: poseData.detectionConfidence,
          sampleCoord: poseData.screenCoordinates['left_hip'] || 'N/A'
        });

        setCurrentPose(poseData);
        onPoseData?.(poseData);

        // Log render state
        console.log('🎨 LiveSVGOverlay render state:', {
          hasPoseData: true,
          isAnalyzing,
          svgElement: document.querySelector('svg.pose-overlay') ? 'EXISTS' : 'MISSING'
        });
      } else {
        console.log('❌ LiveSVGOverlay no pose in frame');
        setCurrentPose(null);
        onPoseData?.(null);
      }
    } catch (err) {
      console.error('⚠️ LiveSVGOverlay frame analysis error:', err);
    }
  }, [videoRef, isPlaying, onPoseData, isAnalyzing]);

  // Animation loop for real-time analysis
  useEffect(() => {
    if (!isAnalyzing || !isPlaying) {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      return;
    }

    const analysisLoop = () => {
      analyzeCurrentFrame();
      animationFrameRef.current = requestAnimationFrame(analysisLoop);
    };

    animationFrameRef.current = requestAnimationFrame(analysisLoop);

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [isAnalyzing, isPlaying, analyzeCurrentFrame]);

  // Reset stats when video changes
  useEffect(() => {
    analysisStatsRef.current = {
      frameCount: 0,
      detectionCount: 0,
      totalConfidence: 0,
      lastUpdateTime: 0
    };
    setCurrentPose(null);
  }, [videoRef.current?.src]);





  // Render skeletal connections
  const renderSkeletalConnections = (poseData: LivePoseData) => {
    const connections = analyzerRef.current?.getSkeletalConnections() || [];
    const { screenCoordinates } = poseData;

    console.log('🔗 Rendering connections:', {
      connectionCount: connections.length,
      coordinateCount: Object.keys(screenCoordinates).length
    });

    return connections
      .filter(conn => {
        const hasFrom = screenCoordinates[conn.from] && screenCoordinates[conn.from].confidence > 0.3;
        const hasTo = screenCoordinates[conn.to] && screenCoordinates[conn.to].confidence > 0.3;
        return hasFrom && hasTo;
      })
      .map((conn, index) => {
        const fromPoint = screenCoordinates[conn.from];
        const toPoint = screenCoordinates[conn.to];

        // Log first connection for debugging
        if (index === 0) {
          console.log('🔗 First connection:', {
            from: conn.from,
            to: conn.to,
            fromCoord: fromPoint,
            toCoord: toPoint
          });
        }

        return (
          <line
            key={`connection-${index}`}
            x1={fromPoint.x}
            y1={fromPoint.y}
            x2={toPoint.x}
            y2={toPoint.y}
            stroke={conn.color}
            strokeWidth="0.5"  // Adjusted for 0-100 scale
            strokeLinecap="round"
            opacity={Math.min(fromPoint.confidence, toPoint.confidence)}
          />
        );
      });
  };

  // Render joint markers
  const renderJointMarkers = (poseData: LivePoseData) => {
    const { screenCoordinates } = poseData;

    console.log('🔵 Rendering joint markers:', {
      totalJoints: Object.keys(screenCoordinates).length,
      filteredJoints: Object.entries(screenCoordinates).filter(([_, joint]) => joint.confidence > 0.3).length
    });

    return Object.entries(screenCoordinates)
      .filter(([_, joint]) => joint.confidence > 0.3)
      .map(([name, joint], index) => {
        const radius = 1; // Adjusted for 0-100 scale
        const color = getJointColor(name, config.overlayStyle);

        // Log first joint for debugging
        if (index === 0) {
          console.log('🔵 First joint:', {
            name,
            coord: joint,
            radius,
            color
          });
        }

        return (
          <circle
            key={`joint-${name}`}
            cx={joint.x}
            cy={joint.y}
            r={radius}
            fill={color}
            stroke="white"
            strokeWidth="0.2"
            opacity={joint.confidence}
          />
        );
      });
  };

  // Render angle measurements
  const renderAngleMeasurements = (poseData: LivePoseData) => {
    const { jointAngles, screenCoordinates } = poseData;
    const angleElements: JSX.Element[] = [];

    Object.entries(jointAngles).forEach(([angleName, angleValue], index) => {
      let jointName: string;
      let color = '#FF6600';

      // Map angle names to joint positions
      switch (angleName) {
        case 'leftKnee':
          jointName = 'left_knee';
          color = '#FFDD00';
          break;
        case 'rightKnee':
          jointName = 'right_knee';
          color = '#FFDD00';
          break;
        case 'leftAnkle':
          jointName = 'left_ankle';
          color = '#00FF44';
          break;
        case 'rightAnkle':
          jointName = 'right_ankle';
          color = '#00FF44';
          break;
        case 'leftElbow':
          jointName = 'left_elbow';
          color = '#FF8800';
          break;
        case 'rightElbow':
          jointName = 'right_elbow';
          color = '#FF8800';
          break;
        default:
          return;
      }

      const joint = screenCoordinates[jointName];
      if (!joint || joint.confidence < 0.5) return;

      const radius = 25;
      const angleText = `${Math.round(angleValue)}°`;

      // Angle arc (simplified - could be enhanced with proper arc calculation)
      angleElements.push(
        <g key={`angle-${index}`}>
          <circle
            cx={joint.x}
            cy={joint.y}
            r={radius}
            fill="none"
            stroke={color}
            strokeWidth="2"
            opacity="0.6"
            strokeDasharray="10,5"
          />
          <text
            x={joint.x + radius + 5}
            y={joint.y - 5}
            fill={color}
            fontSize="14"
            fontWeight="bold"
            fontFamily="monospace"
            filter="url(#textShadow)"
          >
            {angleText}
          </text>
        </g>
      );
    });

    return angleElements;
  };

  // Get joint radius based on importance and confidence
  const getJointRadius = (name: string, confidence: number): number => {
    const baseRadius = config.overlayStyle === 'minimal' ? 3 : 5;
    let multiplier = 1;

    // Key joints get larger markers
    if (name.includes('hip') || name.includes('knee') || name.includes('ankle')) {
      multiplier = 1.2;
    }
    if (name.includes('shoulder') || name === 'nose') {
      multiplier = 1.1;
    }

    return Math.max(2, baseRadius * multiplier * confidence);
  };

  // Get joint color based on type and style
  const getJointColor = (name: string, style: string): string => {
    if (style === 'minimal') return '#FFFFFF';

    const colorMap: { [key: string]: string } = {
      hip: '#FF4444',
      knee: '#FFDD00',
      ankle: '#00FF44',
      foot: '#00CC33',
      shoulder: '#FF8800',
      elbow: '#FFAA00',
      wrist: '#FFCC00',
      default: '#88AAFF'
    };

    for (const [type, color] of Object.entries(colorMap)) {
      if (name.includes(type)) {
        return style === 'athletic' ? adjustColorBrightness(color, 20) : color;
      }
    }

    return colorMap.default;
  };

  // Utility to adjust color brightness for athletic style
  const adjustColorBrightness = (hex: string, percent: number): string => {
    const num = parseInt(hex.replace('#', ''), 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) + amt;
    const G = (num >> 8 & 0x00FF) + amt;
    const B = (num & 0x0000FF) + amt;
    return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
      (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
      (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
  };

  // Render 3D info overlay
  const render3DInfoOverlay = (poseData: LivePoseData) => {
    return (
      <g className="info-overlay">
        <rect
          x="10"
          y="10"
          width="200"
          height="60"
          fill="rgba(0, 0, 0, 0.7)"
          rx="5"
          ry="5"
        />
        <text x="20" y="30" fill="#00FF88" fontSize="12" fontWeight="bold" fontFamily="monospace">
          3D POSE ANALYSIS
        </text>
        <text x="20" y="45" fill="#FFFFFF" fontSize="10" fontFamily="monospace">
          Scale: {poseData.realWorldScale.toFixed(3)}
        </text>
        <text x="20" y="58" fill="#FFFFFF" fontSize="10" fontFamily="monospace">
          Confidence: {(poseData.detectionConfidence * 100).toFixed(0)}%
        </text>
      </g>
    );
  };

  // Error state
  if (error) {
    return (
      <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
        <div className="bg-red-600 text-white px-4 py-3 rounded-lg">
          <h3 className="font-semibold">Analysis Error</h3>
          <p className="text-sm mt-1">{error}</p>
        </div>
      </div>
    );
  }

  // Loading state
  if (!isAnalyzing) {
    return (
      <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
        <div className="bg-blue-600 text-white px-4 py-3 rounded-lg flex items-center">
          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3" />
          <span>Initializing 3D Analysis...</span>
        </div>
      </div>
    );
  }

  return (
    <svg
      className="absolute inset-0 w-full h-full pointer-events-none z-30 pose-overlay"
      viewBox="0 0 100 100"  // CRITICAL FIX: Use 0-100 coordinate system
      preserveAspectRatio="none"
      data-analyzer-ready={isAnalyzing ? 'true' : 'false'}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%'
      }}
    >
      <defs>
        {/* Glow effect for connections */}
        <filter id="glowEffect">
          <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
        
        {/* Shadow effect for joints */}
        <filter id="shadowEffect">
          <feDropShadow dx="1" dy="1" stdDeviation="1" floodColor="black" floodOpacity="0.3"/>
        </filter>
        
        {/* Text shadow */}
        <filter id="textShadow">
          <feDropShadow dx="1" dy="1" stdDeviation="1" floodColor="black" floodOpacity="0.5"/>
        </filter>
      </defs>

      {currentPose ? (
        <g className="pose-data-group">
          {/* Test circle to verify SVG is rendering */}
          <circle cx="50" cy="50" r="5" fill="red" opacity="1" />

          {/* Debug text */}
          <text x="10" y="10" fill="lime" fontSize="5" fontFamily="monospace">
            POSE DETECTED
          </text>

          {/* Skeletal connections */}
          <g className="skeletal-connections">
            {renderSkeletalConnections(currentPose)}
          </g>

          {/* Joint markers */}
          <g className="joint-markers">
            {renderJointMarkers(currentPose)}
          </g>

          {/* Angle measurements */}
          {config.overlayStyle !== 'minimal' && (
            <g className="angle-measurements">
              {renderAngleMeasurements(currentPose)}
            </g>
          )}
        </g>
      ) : (
        <g className="no-pose-detected">
          {/* Test rectangle to verify SVG is rendering */}
          <rect x="10" y="10" width="80" height="10" fill="yellow" opacity="0.5" />
          <text x="50" y="15" fill="red" fontSize="5" fontFamily="monospace" textAnchor="middle">
            NO POSE DETECTED
          </text>
        </g>
      )}
    </svg>
  );
};

export default LiveSVGOverlay;