/**
 * FIXED VIDEO UPLOADER WITH INTEGRATED 3D CONFIGURATION
 * 
 * This moves the 3D processing configuration to the upload page as requested,
 * adds imperial height input with metric conversion, and streamlines the UI flow.
 */

import React, { useState, useRef } from 'react';
import { ActivityType, VideoType } from '../types';
import { Upload, Video, Play, Settings, Ruler, Zap } from 'lucide-react';

interface VideoUploaderProps {
  activity: ActivityType;
  onVideoUpload: (videoType: VideoType, file: File | null) => Promise<string | null>;
  uploadedVideos: Record<string, string>;
  onStartAnalysis: () => void;
  isAnalyzing: boolean;
}

interface ProcessingConfig {
  heightFeet: number;
  heightInches: number;
  overlayStyle: 'medical' | 'athletic' | 'minimal';
  processEveryFrame: boolean;
}

const VideoUploader: React.FC<VideoUploaderProps> = ({
  activity,
  onVideoUpload,
  uploadedVideos,
  onStartAnalysis,
  isAnalyzing
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [processingConfig, setProcessingConfig] = useState<ProcessingConfig>({
    heightFeet: 5,
    heightInches: 10,
    overlayStyle: 'medical',
    processEveryFrame: true
  });

  const sideFileRef = useRef<HTMLInputElement>(null);
  const rearFileRef = useRef<HTMLInputElement>(null);

  // Convert imperial to metric
  const getHeightInMeters = (): number => {
    const totalInches = processingConfig.heightFeet * 12 + processingConfig.heightInches;
    return totalInches * 0.0254; // Convert inches to meters
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = async (e: React.DragEvent, videoType: VideoType) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      await handleFileUpload(e.dataTransfer.files[0], videoType);
    }
  };

  const handleFileUpload = async (file: File, videoType: VideoType) => {
    if (!file) return;

    // Validate file type
    const validTypes = ['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/webm'];
    if (!validTypes.includes(file.type) && !file.name.toLowerCase().match(/\.(mp4|mov|avi|webm)$/)) {
      alert('Please upload a valid video file (MP4, MOV, AVI, or WebM)');
      return;
    }

    // Validate file size (100MB limit)
    const maxSize = 100 * 1024 * 1024;
    if (file.size > maxSize) {
      alert('File size must be less than 100MB');
      return;
    }

    try {
      setUploadProgress(prev => ({ ...prev, [videoType]: 0 }));
      
      // Simulate upload progress (replace with actual progress tracking if available)
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const current = prev[videoType] || 0;
          if (current >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return { ...prev, [videoType]: current + 10 };
        });
      }, 100);

      const result = await onVideoUpload(videoType, file);
      
      clearInterval(progressInterval);
      setUploadProgress(prev => ({ ...prev, [videoType]: 100 }));
      
      // Clear progress after 2 seconds
      setTimeout(() => {
        setUploadProgress(prev => {
          const newProgress = { ...prev };
          delete newProgress[videoType];
          return newProgress;
        });
      }, 2000);

      if (result) {
        console.log(`✅ ${videoType} video uploaded successfully`);
      }
    } catch (error) {
      console.error(`❌ Error uploading ${videoType} video:`, error);
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[videoType];
        return newProgress;
      });
      alert(`Failed to upload ${videoType} video. Please try again.`);
    }
  };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>, videoType: VideoType) => {
    if (e.target.files && e.target.files[0]) {
      await handleFileUpload(e.target.files[0], videoType);
    }
  };

  const removeVideo = async (videoType: VideoType) => {
    await onVideoUpload(videoType, null);
  };

  const canStartAnalysis = () => {
    return uploadedVideos.side && !isAnalyzing;
  };

  const handleStartAnalysis = () => {
    if (!canStartAnalysis()) return;
    
    // Store processing config globally for use by analysis components
    (window as any).poseAnalysisConfig = {
      userHeightMeters: getHeightInMeters(),
      overlayStyle: processingConfig.overlayStyle,
      processEveryFrame: processingConfig.processEveryFrame
    };
    
    console.log('🎯 Starting 3D analysis with config:', {
      heightFeet: processingConfig.heightFeet,
      heightInches: processingConfig.heightInches,
      heightMeters: getHeightInMeters(),
      overlayStyle: processingConfig.overlayStyle,
      processEveryFrame: processingConfig.processEveryFrame
    });
    
    onStartAnalysis();
  };

  const renderVideoUploadZone = (videoType: VideoType, label: string, description: string) => {
    const hasVideo = uploadedVideos[videoType];
    const progress = uploadProgress[videoType];
    const fileRef = videoType === 'side' ? sideFileRef : rearFileRef;

    return (
      <div
        className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300 ${
          dragActive 
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
            : hasVideo 
              ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={(e) => handleDrop(e, videoType)}
      >
        <input
          ref={fileRef}
          type="file"
          accept="video/*"
          onChange={(e) => handleFileSelect(e, videoType)}
          className="hidden"
        />

        {hasVideo ? (
          <div className="space-y-4">
            <div className="flex items-center justify-center">
              <Video className="w-12 h-12 text-green-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-green-800 dark:text-green-200">
                {label} Video Uploaded
              </h3>
              <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                Ready for 3D analysis
              </p>
            </div>
            <div className="flex justify-center space-x-3">
              <button
                onClick={() => fileRef.current?.click()}
                className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition"
              >
                Replace Video
              </button>
              <button
                onClick={() => removeVideo(videoType)}
                className="px-4 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition"
              >
                Remove
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {progress !== undefined ? (
              <div className="space-y-3">
                <Upload className="w-12 h-12 text-blue-600 mx-auto animate-pulse" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                    Uploading {label} Video...
                  </h3>
                  <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {progress}% complete
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                    Upload {label} Video
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {description}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                    MP4, MOV, AVI, or WebM • Max 100MB
                  </p>
                </div>
                <button
                  onClick={() => fileRef.current?.click()}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition flex items-center mx-auto"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Choose File
                </button>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  or drag and drop here
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          3D {activity === 'running' ? 'Running' : 'Cycling'} Analysis
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
          Upload your videos and configure 3D pose analysis settings for professional biomechanical analysis 
          with height-based scaling and real-world measurements.
        </p>
      </div>

      {/* Video Upload Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {renderVideoUploadZone('side', 'Side View', 'Record from the side to capture leg extension and body lean')}
        {renderVideoUploadZone('rear', 'Rear View', 'Record from behind to analyze stride symmetry and foot placement')}
      </div>

      {/* 3D Processing Configuration */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex items-center mb-6">
          <Settings className="w-6 h-6 text-blue-600 mr-3" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            3D Processing Configuration
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Height Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              <Ruler className="w-4 h-4 inline mr-2" />
              Your Height
            </label>
            <div className="flex space-x-3">
              <div>
                <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">Feet</label>
                <select
                  value={processingConfig.heightFeet}
                  onChange={(e) => setProcessingConfig(prev => ({
                    ...prev,
                    heightFeet: parseInt(e.target.value)
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700"
                >
                  {[4, 5, 6, 7].map(feet => (
                    <option key={feet} value={feet}>{feet}'</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">Inches</label>
                <select
                  value={processingConfig.heightInches}
                  onChange={(e) => setProcessingConfig(prev => ({
                    ...prev,
                    heightInches: parseInt(e.target.value)
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700"
                >
                  {Array.from({length: 12}, (_, i) => (
                    <option key={i} value={i}>{i}"</option>
                  ))}
                </select>
              </div>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              = {getHeightInMeters().toFixed(2)}m ({processingConfig.heightFeet}'{processingConfig.heightInches}")
            </p>
          </div>

          {/* Overlay Style */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Overlay Style
            </label>
            <select
              value={processingConfig.overlayStyle}
              onChange={(e) => setProcessingConfig(prev => ({
                ...prev,
                overlayStyle: e.target.value as 'medical' | 'athletic' | 'minimal'
              }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700"
            >
              <option value="medical">Medical (Detailed)</option>
              <option value="athletic">Athletic (Dynamic)</option>
              <option value="minimal">Minimal (Clean)</option>
            </select>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              {processingConfig.overlayStyle === 'medical' && 'Detailed joint markers and angle measurements'}
              {processingConfig.overlayStyle === 'athletic' && 'Dynamic colors with performance focus'}
              {processingConfig.overlayStyle === 'minimal' && 'Clean lines for basic analysis'}
            </p>
          </div>

          {/* Processing Quality */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Analysis Quality
            </label>
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="radio"
                  checked={processingConfig.processEveryFrame}
                  onChange={() => setProcessingConfig(prev => ({
                    ...prev,
                    processEveryFrame: true
                  }))}
                  className="mr-2"
                />
                <span className="text-sm">Maximum (Every Frame)</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  checked={!processingConfig.processEveryFrame}
                  onChange={() => setProcessingConfig(prev => ({
                    ...prev,
                    processEveryFrame: false
                  }))}
                  className="mr-2"
                />
                <span className="text-sm">Balanced (Every 2nd Frame)</span>
              </label>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              Higher quality = longer processing time
            </p>
          </div>
        </div>

        {/* Technical Specifications */}
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
          <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
            Technical Specifications
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
            <div>
              <span className="font-medium text-gray-600 dark:text-gray-400">Model:</span>
              <p className="text-gray-800 dark:text-gray-200">BlazePose Full 3D</p>
            </div>
            <div>
              <span className="font-medium text-gray-600 dark:text-gray-400">Keypoints:</span>
              <p className="text-gray-800 dark:text-gray-200">33 3D Landmarks</p>
            </div>
            <div>
              <span className="font-medium text-gray-600 dark:text-gray-400">Precision:</span>
              <p className="text-gray-800 dark:text-gray-200">Real-world measurements</p>
            </div>
            <div>
              <span className="font-medium text-gray-600 dark:text-gray-400">Output:</span>
              <p className="text-gray-800 dark:text-gray-200">Live SVG overlay</p>
            </div>
          </div>
        </div>
      </div>

      {/* Start Analysis Button */}
      <div className="flex justify-center">
        <button
          onClick={handleStartAnalysis}
          disabled={!canStartAnalysis()}
          className={`px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 flex items-center ${
            canStartAnalysis()
              ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white hover:from-blue-700 hover:to-green-700 shadow-lg hover:shadow-xl transform hover:scale-105'
              : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
          }`}
        >
          {isAnalyzing ? (
            <>
              <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin mr-3" />
              Processing 3D Analysis...
            </>
          ) : (
            <>
              <Zap className="w-6 h-6 mr-3" />
              Start 3D Analysis
            </>
          )}
        </button>
      </div>

      {/* Upload Instructions */}
      <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg border border-blue-200 dark:border-blue-700">
        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">
          📋 Recording Tips for Best Results
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700 dark:text-blue-300">
          <div>
            <h4 className="font-semibold mb-2">Side View:</h4>
            <ul className="space-y-1 list-disc list-inside">
              <li>Record perpendicular to runner's path</li>
              <li>Capture full body in frame</li>
              <li>Maintain steady camera position</li>
              <li>Good lighting on runner</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-2">Rear View:</h4>
            <ul className="space-y-1 list-disc list-inside">
              <li>Follow runner from behind</li>
              <li>Keep camera level and steady</li>
              <li>Capture stride symmetry</li>
              <li>Maintain consistent distance</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoUploader;