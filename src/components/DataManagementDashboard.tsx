import React, { useState, useEffect } from 'react';
import { Download, Database, BarChart3, FileText, Calendar, Filter, RefreshCw } from 'lucide-react';
import { DataExporter, ExportOptions, DatasetSummary } from '../utils/dataExporter';

const DataManagementDashboard: React.FC = () => {
  const [dataExporter] = useState(new DataExporter());
  const [summary, setSummary] = useState<DatasetSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [exporting, setExporting] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'csv',
    includeRawKeypoints: false
  });

  useEffect(() => {
    loadSummary();
  }, []);

  const loadSummary = async () => {
    try {
      setLoading(true);
      const summaryData = await dataExporter.getDatasetSummary();
      setSummary(summaryData);
    } catch (error) {
      console.error('Failed to load dataset summary:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (type: 'pose_data' | 'metrics' | 'recommendations') => {
    try {
      setExporting(true);
      let blob: Blob;
      let filename: string;

      switch (type) {
        case 'pose_data':
          blob = await dataExporter.exportPoseData(exportOptions);
          filename = `pose_data_${new Date().toISOString().split('T')[0]}.${exportOptions.format}`;
          break;
        case 'metrics':
          blob = await dataExporter.exportPerformanceMetrics();
          filename = `performance_metrics_${new Date().toISOString().split('T')[0]}.json`;
          break;
        case 'recommendations':
          blob = await dataExporter.exportRecommendations();
          filename = `recommendations_${new Date().toISOString().split('T')[0]}.json`;
          break;
      }

      dataExporter.downloadBlob(blob, filename);
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
          <span className="ml-2 text-lg">Loading dataset summary...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center space-x-2">
          <Database className="w-8 h-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">Data Management Dashboard</h1>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Export and manage pose analysis datasets for data science research and biomechanical analysis.
        </p>
      </div>

      {/* Dataset Summary */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Sessions</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(summary.totalSessions)}</p>
              </div>
              <BarChart3 className="w-8 h-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Frames</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(summary.totalFrames)}</p>
              </div>
              <FileText className="w-8 h-8 text-green-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Videos</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(summary.totalVideos)}</p>
              </div>
              <Database className="w-8 h-8 text-purple-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Detection Rate</p>
                <p className="text-2xl font-bold text-gray-900">{Math.round(summary.averageDetectionRate)}%</p>
              </div>
              <Calendar className="w-8 h-8 text-orange-600" />
            </div>
          </div>
        </div>
      )}

      {/* Activity Breakdown */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-4">Activity Breakdown</h3>
            <div className="space-y-3">
              {Object.entries(summary.activityBreakdown).map(([activity, count]) => (
                <div key={activity} className="flex justify-between items-center">
                  <span className="capitalize text-gray-700">{activity}</span>
                  <span className="font-semibold text-gray-900">{count} videos</span>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-4">View Type Breakdown</h3>
            <div className="space-y-3">
              {Object.entries(summary.viewTypeBreakdown).map(([viewType, count]) => (
                <div key={viewType} className="flex justify-between items-center">
                  <span className="capitalize text-gray-700">{viewType} view</span>
                  <span className="font-semibold text-gray-900">{count} videos</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Export Configuration */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold mb-4">Export Configuration</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Export Format
            </label>
            <select
              value={exportOptions.format}
              onChange={(e) => setExportOptions(prev => ({ ...prev, format: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="csv">CSV (Spreadsheet)</option>
              <option value="json">JSON (Structured)</option>
              <option value="parquet">Parquet (Coming Soon)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Activity Filter
            </label>
            <select
              value={exportOptions.activityType || ''}
              onChange={(e) => setExportOptions(prev => ({ 
                ...prev, 
                activityType: e.target.value ? e.target.value as any : undefined 
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Activities</option>
              <option value="running">Running Only</option>
              <option value="cycling">Cycling Only</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              View Filter
            </label>
            <select
              value={exportOptions.viewType || ''}
              onChange={(e) => setExportOptions(prev => ({ 
                ...prev, 
                viewType: e.target.value ? e.target.value as any : undefined 
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Views</option>
              <option value="side">Side View Only</option>
              <option value="rear">Rear View Only</option>
            </select>
          </div>
        </div>

        <div className="mt-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={exportOptions.includeRawKeypoints}
              onChange={(e) => setExportOptions(prev => ({ 
                ...prev, 
                includeRawKeypoints: e.target.checked 
              }))}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Include raw keypoint data (larger file size)</span>
          </label>
        </div>
      </div>

      {/* Export Actions */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold mb-4">Export Data</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => handleExport('pose_data')}
            disabled={exporting}
            className="flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            <Download className="w-5 h-5 mr-2" />
            {exporting ? 'Exporting...' : 'Export Pose Data'}
          </button>

          <button
            onClick={() => handleExport('metrics')}
            disabled={exporting}
            className="flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            <BarChart3 className="w-5 h-5 mr-2" />
            {exporting ? 'Exporting...' : 'Export Metrics'}
          </button>

          <button
            onClick={() => handleExport('recommendations')}
            disabled={exporting}
            className="flex items-center justify-center px-4 py-3 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            <FileText className="w-5 h-5 mr-2" />
            {exporting ? 'Exporting...' : 'Export Recommendations'}
          </button>
        </div>

        <div className="mt-4 p-4 bg-gray-50 rounded-md">
          <h4 className="font-medium text-gray-900 mb-2">Export Information</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• <strong>Pose Data:</strong> Frame-by-frame joint angles, positions, and metrics</li>
            <li>• <strong>Metrics:</strong> Calculated performance metrics and biomechanical analysis</li>
            <li>• <strong>Recommendations:</strong> AI-generated equipment and form recommendations</li>
          </ul>
        </div>
      </div>

      {/* Data Science Integration */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          Data Science Integration
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Dataset Features</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Timestamped pose data with joint coordinates</li>
              <li>• Biomechanical metrics (angles, stride length, posture)</li>
              <li>• Video metadata (resolution, FPS, duration)</li>
              <li>• Detection confidence scores</li>
              <li>• Activity and view type labels</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Use Cases</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Machine learning model training</li>
              <li>• Biomechanical pattern analysis</li>
              <li>• Equipment recommendation algorithms</li>
              <li>• Performance optimization research</li>
              <li>• Injury prevention studies</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Date Range */}
      {summary && summary.dateRange.earliest && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4">Dataset Timeline</h3>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Earliest Data</p>
              <p className="font-semibold">{formatDate(summary.dateRange.earliest)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Latest Data</p>
              <p className="font-semibold">{formatDate(summary.dateRange.latest)}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataManagementDashboard;
