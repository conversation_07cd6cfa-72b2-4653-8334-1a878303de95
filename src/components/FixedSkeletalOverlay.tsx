/**
 * FIXED DYNAMIC SKELETAL OVERLAY COMPONENT
 * 
 * This replaces the broken static skeletal overlay with a dynamic one that:
 * 1. Uses real-time pose detection instead of pre-stored database coordinates
 * 2. Implements proper ROI tracking and dynamic scaling
 * 3. Follows the person's movement across the video
 * 4. Scales with the person's distance from camera
 * 5. Updates coordinates in real-time
 */

import React, { useState, useEffect, useRef } from 'react';
import { ActivityType } from '../types';
import { analyzePoseDynamic, resetDynamicTracking, RealTimeSkeleton } from '../utils/FixedDynamicPoseAnalysis';

interface FixedSkeletalOverlayProps {
  activity: ActivityType;
  view: 'side' | 'rear';
  currentTime: number;
  duration: number;
  videoRef: React.RefObject<HTMLVideoElement>;
  sessionId?: string;
}

interface BiomechanicalJoint {
  x: number;
  y: number;
  confidence: number;
  detected: boolean;
}

interface DynamicSkeleton {
  // Core detected joints
  hip: BiomechanicalJoint;
  knee: BiomechanicalJoint;
  ankle: BiomechanicalJoint;
  shoulder: BiomechanicalJoint;
  elbow: BiomechanicalJoint;
  wrist: BiomechanicalJoint;
  neck: BiomechanicalJoint;
  
  // Enhanced joints
  heel: BiomechanicalJoint;
  foot: BiomechanicalJoint;
  
  // Bilateral joints
  leftShoulder: BiomechanicalJoint;
  rightShoulder: BiomechanicalJoint;
  leftHip: BiomechanicalJoint;
  rightHip: BiomechanicalJoint;
  leftKnee: BiomechanicalJoint;
  rightKnee: BiomechanicalJoint;
  leftAnkle: BiomechanicalJoint;
  rightAnkle: BiomechanicalJoint;
  
  // Center of mass and dynamic properties
  centerOfMass: { x: number; y: number };
  scale: number;
  confidence: number;
}

const FixedSkeletalOverlay: React.FC<FixedSkeletalOverlayProps> = ({
  activity,
  view,
  currentTime,
  videoRef,
  sessionId
}) => {
  const [realTimeSkeleton, setRealTimeSkeleton] = useState<RealTimeSkeleton | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const analysisIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastAnalysisTime = useRef<number>(-1);

  // Reset tracking when video changes
  useEffect(() => {
    resetDynamicTracking();
    setRealTimeSkeleton(null);
    lastAnalysisTime.current = -1;
  }, [sessionId]);

  // CRITICAL FIX: Real-time pose analysis
  useEffect(() => {
    const performRealTimeAnalysis = async () => {
      if (!videoRef.current || isAnalyzing) return;

      const video = videoRef.current;
      
      // Check if video is ready and playing
      if (video.readyState < 2 || video.videoWidth === 0 || video.videoHeight === 0) {
        return;
      }

      // Skip if we've already analyzed this exact timestamp
      const currentVideoTime = video.currentTime;
      if (Math.abs(currentVideoTime - lastAnalysisTime.current) < 0.033) { // ~30fps throttling
        return;
      }

      try {
        setIsAnalyzing(true);
        setError(null);

        console.log(`🔄 Real-time analysis at ${currentVideoTime.toFixed(3)}s`);
        
        const skeleton = await analyzePoseDynamic(video, currentVideoTime);
        setRealTimeSkeleton(skeleton);
        lastAnalysisTime.current = currentVideoTime;

        console.log(`✅ Dynamic skeleton updated - Center: (${skeleton.dynamicData.centerOfMass.x.toFixed(1)}, ${skeleton.dynamicData.centerOfMass.y.toFixed(1)})`);
        
      } catch (err) {
        console.error('Real-time pose analysis failed:', err);
        setError(err instanceof Error ? err.message : 'Analysis failed');
      } finally {
        setIsAnalyzing(false);
      }
    };

    // Set up real-time analysis interval
    if (analysisIntervalRef.current) {
      clearInterval(analysisIntervalRef.current);
    }

    analysisIntervalRef.current = setInterval(performRealTimeAnalysis, 100); // 10fps analysis

    return () => {
      if (analysisIntervalRef.current) {
        clearInterval(analysisIntervalRef.current);
        analysisIntervalRef.current = null;
      }
    };
  }, [videoRef, currentTime, isAnalyzing]);

  // Build dynamic skeleton from real-time detection
  const buildDynamicSkeleton = (skeleton: RealTimeSkeleton): DynamicSkeleton => {
    const { keypoints, dynamicData } = skeleton;
    
    // Helper function to create joint
    const createJoint = (name: string, fallback?: { x: number; y: number }): BiomechanicalJoint => {
      const detected = keypoints[name];
      if (detected && detected.confidence > 0.3) {
        return {
          x: detected.x,
          y: detected.y,
          confidence: detected.confidence,
          detected: true
        };
      }
      
      // Use fallback position relative to center of mass
      const fallbackPos = fallback || { x: dynamicData.centerOfMass.x, y: dynamicData.centerOfMass.y };
      return {
        x: fallbackPos.x,
        y: fallbackPos.y,
        confidence: 0.3,
        detected: false
      };
    };

    // Determine best side for primary joints
    const leftShoulder = keypoints['left_shoulder'];
    const rightShoulder = keypoints['right_shoulder'];
    const leftHip = keypoints['left_hip'];
    const rightHip = keypoints['right_hip'];
    
    const leftConfidence = (leftShoulder?.confidence || 0) + (leftHip?.confidence || 0);
    const rightConfidence = (rightShoulder?.confidence || 0) + (rightHip?.confidence || 0);
    const useLeftSide = leftConfidence >= rightConfidence;

    console.log(`🎯 Using ${useLeftSide ? 'left' : 'right'} side (L:${leftConfidence.toFixed(2)} R:${rightConfidence.toFixed(2)})`);

    return {
      // Primary joints (best detected side)
      hip: createJoint(useLeftSide ? 'left_hip' : 'right_hip', 
        { x: dynamicData.centerOfMass.x, y: dynamicData.centerOfMass.y + 15 }),
      knee: createJoint(useLeftSide ? 'left_knee' : 'right_knee',
        { x: dynamicData.centerOfMass.x, y: dynamicData.centerOfMass.y + 30 }),
      ankle: createJoint(useLeftSide ? 'left_ankle' : 'right_ankle',
        { x: dynamicData.centerOfMass.x, y: dynamicData.centerOfMass.y + 45 }),
      shoulder: createJoint(useLeftSide ? 'left_shoulder' : 'right_shoulder',
        { x: dynamicData.centerOfMass.x, y: dynamicData.centerOfMass.y - 15 }),
      elbow: createJoint(useLeftSide ? 'left_elbow' : 'right_elbow',
        { x: dynamicData.centerOfMass.x + (useLeftSide ? -10 : 10), y: dynamicData.centerOfMass.y }),
      wrist: createJoint(useLeftSide ? 'left_wrist' : 'right_wrist',
        { x: dynamicData.centerOfMass.x + (useLeftSide ? -15 : 15), y: dynamicData.centerOfMass.y + 5 }),
      neck: createJoint('nose', { x: dynamicData.centerOfMass.x, y: dynamicData.centerOfMass.y - 25 }),
      
      // Enhanced joints
      heel: createJoint(useLeftSide ? 'left_heel' : 'right_heel',
        { x: dynamicData.centerOfMass.x - 3, y: dynamicData.centerOfMass.y + 47 }),
      foot: createJoint(useLeftSide ? 'left_foot_index' : 'right_foot_index',
        { x: dynamicData.centerOfMass.x + 5, y: dynamicData.centerOfMass.y + 48 }),
      
      // Bilateral joints
      leftShoulder: createJoint('left_shoulder'),
      rightShoulder: createJoint('right_shoulder'),
      leftHip: createJoint('left_hip'),
      rightHip: createJoint('right_hip'),
      leftKnee: createJoint('left_knee'),
      rightKnee: createJoint('right_knee'),
      leftAnkle: createJoint('left_ankle'),
      rightAnkle: createJoint('right_ankle'),
      
      // Dynamic properties
      centerOfMass: dynamicData.centerOfMass,
      scale: dynamicData.scale,
      confidence: dynamicData.confidence
    };
  };

  // Calculate angle between three points
  const calculateAngle = (p1: BiomechanicalJoint, vertex: BiomechanicalJoint, p2: BiomechanicalJoint): number => {
    const vector1 = { x: p1.x - vertex.x, y: p1.y - vertex.y };
    const vector2 = { x: p2.x - vertex.x, y: p2.y - vertex.y };
    
    const dot = vector1.x * vector2.x + vector1.y * vector2.y;
    const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y);
    const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y);
    
    if (mag1 === 0 || mag2 === 0) return 180;
    
    const cosAngle = Math.max(-1, Math.min(1, dot / (mag1 * mag2)));
    return Math.acos(cosAngle) * (180 / Math.PI);
  };

  const renderDynamicSkeleton = () => {
    if (!realTimeSkeleton || !videoRef.current) {
      return (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="text-white text-sm">
            {isAnalyzing ? 'Analyzing pose...' : 'No real-time detection available'}
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="absolute top-2 left-2 bg-red-500 text-white px-3 py-2 rounded text-sm">
          Analysis Error: {error}
        </div>
      );
    }

    const skeleton = buildDynamicSkeleton(realTimeSkeleton);
    const video = videoRef.current;
    
    // Dynamic viewBox calculation based on video aspect ratio
    const aspectRatio = video.videoHeight / video.videoWidth;
    const viewBoxHeight = 100 * aspectRatio;

    // CRITICAL FIX: Dynamic scaling and positioning
    const scaleFactor = skeleton.scale;
    const centerX = skeleton.centerOfMass.x;
    const centerY = skeleton.centerOfMass.y;

    console.log(`🎨 Rendering skeleton - Center: (${centerX.toFixed(1)}, ${centerY.toFixed(1)}), Scale: ${scaleFactor.toFixed(2)}`);

    // Calculate key angles
    const hipAngle = calculateAngle(skeleton.shoulder, skeleton.hip, skeleton.knee);
    const kneeAngle = calculateAngle(skeleton.hip, skeleton.knee, skeleton.ankle);
    const ankleAngle = calculateAngle(skeleton.knee, skeleton.ankle, skeleton.foot);

    return (
      <svg
        className="absolute inset-0 w-full h-full pointer-events-none"
        viewBox={`0 0 100 ${viewBoxHeight}`}
        preserveAspectRatio="none"
        style={{ zIndex: 50 }}
      >
        <defs>
          <filter id="dynamicGlow">
            <feGaussianBlur stdDeviation="0.4" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          
          <linearGradient id="confidenceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#00FF88" stopOpacity="0.9"/>
            <stop offset="100%" stopColor="#0088FF" stopOpacity="0.7"/>
          </linearGradient>
        </defs>

        {/* DYNAMIC POSITIONING: All elements positioned relative to detected center */}
        <g transform={`translate(${centerX - 50}, ${centerY - 50}) scale(${scaleFactor})`}>
          
          {/* Primary skeleton - detected side with high confidence */}
          <g className="primary-skeleton" opacity={skeleton.confidence}>
            {/* Core body structure */}
            <line x1={skeleton.neck.x - centerX + 50} y1={skeleton.neck.y - centerY + 50}
                  x2={skeleton.shoulder.x - centerX + 50} y2={skeleton.shoulder.y - centerY + 50}
                  stroke="#0088FF" strokeWidth="2" strokeLinecap="round" filter="url(#dynamicGlow)"/>
            
            <line x1={skeleton.shoulder.x - centerX + 50} y1={skeleton.shoulder.y - centerY + 50}
                  x2={skeleton.hip.x - centerX + 50} y2={skeleton.hip.y - centerY + 50}
                  stroke="#0088FF" strokeWidth="2.5" strokeLinecap="round" filter="url(#dynamicGlow)"/>
            
            {/* Primary leg */}
            <line x1={skeleton.hip.x - centerX + 50} y1={skeleton.hip.y - centerY + 50}
                  x2={skeleton.knee.x - centerX + 50} y2={skeleton.knee.y - centerY + 50}
                  stroke="#00AA55" strokeWidth="3" strokeLinecap="round" filter="url(#dynamicGlow)"/>
            
            <line x1={skeleton.knee.x - centerX + 50} y1={skeleton.knee.y - centerY + 50}
                  x2={skeleton.ankle.x - centerX + 50} y2={skeleton.ankle.y - centerY + 50}
                  stroke="#00AA55" strokeWidth="3" strokeLinecap="round" filter="url(#dynamicGlow)"/>
            
            {/* Foot structure */}
            <line x1={skeleton.ankle.x - centerX + 50} y1={skeleton.ankle.y - centerY + 50}
                  x2={skeleton.heel.x - centerX + 50} y2={skeleton.heel.y - centerY + 50}
                  stroke="#00AA55" strokeWidth="2" strokeLinecap="round" filter="url(#dynamicGlow)"/>
            
            <line x1={skeleton.heel.x - centerX + 50} y1={skeleton.heel.y - centerY + 50}
                  x2={skeleton.foot.x - centerX + 50} y2={skeleton.foot.y - centerY + 50}
                  stroke="#00AA55" strokeWidth="2" strokeLinecap="round" filter="url(#dynamicGlow)"/>
            
            {/* Primary arm */}
            <line x1={skeleton.shoulder.x - centerX + 50} y1={skeleton.shoulder.y - centerY + 50}
                  x2={skeleton.elbow.x - centerX + 50} y2={skeleton.elbow.y - centerY + 50}
                  stroke="#FF8800" strokeWidth="2" strokeLinecap="round" filter="url(#dynamicGlow)"/>
            
            <line x1={skeleton.elbow.x - centerX + 50} y1={skeleton.elbow.y - centerY + 50}
                  x2={skeleton.wrist.x - centerX + 50} y2={skeleton.wrist.y - centerY + 50}
                  stroke="#FF8800" strokeWidth="2" strokeLinecap="round" filter="url(#dynamicGlow)"/>
          </g>

          {/* Bilateral skeleton - both sides with reduced opacity */}
          {skeleton.leftShoulder.detected && skeleton.rightShoulder.detected && (
            <g className="bilateral-skeleton" opacity="0.6">
              {/* Left side */}
              {skeleton.leftShoulder.detected && skeleton.leftHip.detected && (
                <line x1={skeleton.leftShoulder.x - centerX + 50} y1={skeleton.leftShoulder.y - centerY + 50}
                      x2={skeleton.leftHip.x - centerX + 50} y2={skeleton.leftHip.y - centerY + 50}
                      stroke="#4488FF" strokeWidth="1.5" strokeLinecap="round"/>
              )}
              
              {skeleton.leftHip.detected && skeleton.leftKnee.detected && (
                <line x1={skeleton.leftHip.x - centerX + 50} y1={skeleton.leftHip.y - centerY + 50}
                      x2={skeleton.leftKnee.x - centerX + 50} y2={skeleton.leftKnee.y - centerY + 50}
                      stroke="#44AA55" strokeWidth="1.5" strokeLinecap="round"/>
              )}
              
              {skeleton.leftKnee.detected && skeleton.leftAnkle.detected && (
                <line x1={skeleton.leftKnee.x - centerX + 50} y1={skeleton.leftKnee.y - centerY + 50}
                      x2={skeleton.leftAnkle.x - centerX + 50} y2={skeleton.leftAnkle.y - centerY + 50}
                      stroke="#44AA55" strokeWidth="1.5" strokeLinecap="round"/>
              )}
              
              {/* Right side */}
              {skeleton.rightShoulder.detected && skeleton.rightHip.detected && (
                <line x1={skeleton.rightShoulder.x - centerX + 50} y1={skeleton.rightShoulder.y - centerY + 50}
                      x2={skeleton.rightHip.x - centerX + 50} y2={skeleton.rightHip.y - centerY + 50}
                      stroke="#4488FF" strokeWidth="1.5" strokeLinecap="round"/>
              )}
              
              {skeleton.rightHip.detected && skeleton.rightKnee.detected && (
                <line x1={skeleton.rightHip.x - centerX + 50} y1={skeleton.rightHip.y - centerY + 50}
                      x2={skeleton.rightKnee.x - centerX + 50} y2={skeleton.rightKnee.y - centerY + 50}
                      stroke="#44AA55" strokeWidth="1.5" strokeLinecap="round"/>
              )}
              
              {skeleton.rightKnee.detected && skeleton.rightAnkle.detected && (
                <line x1={skeleton.rightKnee.x - centerX + 50} y1={skeleton.rightKnee.y - centerY + 50}
                      x2={skeleton.rightAnkle.x - centerX + 50} y2={skeleton.rightAnkle.y - centerY + 50}
                      stroke="#44AA55" strokeWidth="1.5" strokeLinecap="round"/>
              )}
            </g>
          )}

          {/* Joint markers */}
          <g className="joint-markers">
            {/* Primary joints */}
            <circle cx={skeleton.hip.x - centerX + 50} cy={skeleton.hip.y - centerY + 50} r="2"
                    fill={skeleton.hip.detected ? "#FF4444" : "#888888"} stroke="white" strokeWidth="0.5"/>
            
            <circle cx={skeleton.knee.x - centerX + 50} cy={skeleton.knee.y - centerY + 50} r="2.5"
                    fill={skeleton.knee.detected ? "#FFDD00" : "#888888"} stroke="white" strokeWidth="0.5"/>
            
            <circle cx={skeleton.ankle.x - centerX + 50} cy={skeleton.ankle.y - centerY + 50} r="2"
                    fill={skeleton.ankle.detected ? "#00FF44" : "#888888"} stroke="white" strokeWidth="0.5"/>
            
            <circle cx={skeleton.shoulder.x - centerX + 50} cy={skeleton.shoulder.y - centerY + 50} r="1.5"
                    fill={skeleton.shoulder.detected ? "#FF8800" : "#888888"} stroke="white" strokeWidth="0.5"/>
            
            <circle cx={skeleton.neck.x - centerX + 50} cy={skeleton.neck.y - centerY + 50} r="1.5"
                    fill={skeleton.neck.detected ? "#8888FF" : "#888888"} stroke="white" strokeWidth="0.5"/>
          </g>

          {/* Angle displays */}
          <g className="angle-displays">
            {/* Hip angle */}
            <text x={skeleton.hip.x - centerX + 55} y={skeleton.hip.y - centerY + 45}
                  fill="orange" fontSize="3" fontWeight="bold">
              {Math.round(hipAngle)}°
            </text>
            
            {/* Knee angle */}
            <text x={skeleton.knee.x - centerX + 55} y={skeleton.knee.y - centerY + 45}
                  fill="yellow" fontSize="3" fontWeight="bold">
              {Math.round(kneeAngle)}°
            </text>
            
            {/* Ankle angle */}
            <text x={skeleton.ankle.x - centerX + 55} y={skeleton.ankle.y - centerY + 45}
                  fill="lime" fontSize="3" fontWeight="bold">
              {Math.round(ankleAngle)}°
            </text>
          </g>
        </g>

        {/* Dynamic tracking info overlay */}
        <text x="2" y="8" fill="lime" fontSize="2.5" fontFamily="monospace">
          🎯 DYNAMIC: Center({centerX.toFixed(1)}, {centerY.toFixed(1)}) Scale:{scaleFactor.toFixed(2)} Conf:{skeleton.confidence.toFixed(2)}
        </text>
        
        <text x="2" y="12" fill="cyan" fontSize="2" fontFamily="monospace">
          Time: {currentTime.toFixed(2)}s | Detected: {Object.values(skeleton).filter(j => typeof j === 'object' && 'detected' in j && j.detected).length} joints
        </text>
      </svg>
    );
  };

  return (
    <div className="absolute inset-0 pointer-events-none" style={{ zIndex: 20 }}>
      {activity === 'running' && view === 'side' && renderDynamicSkeleton()}
    </div>
  );
};

export default FixedSkeletalOverlay;