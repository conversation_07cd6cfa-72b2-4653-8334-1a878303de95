import React, { useCallback, useState, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { ActivityType, VideoType } from '../types';
import { Upload, Film, CheckCircle2, <PERSON>ader2, AlertCircle, Trash2, <PERSON><PERSON><PERSON><PERSON>gle } from 'lucide-react';
import { motion } from 'framer-motion';
import { validateVideoFormat } from '../utils/videoValidator';

interface VideoUploaderProps {
  activity: ActivityType;
  uploadedVideos: Record<string, string>;
  onVideoUpload: (type: VideoType, file: File | null) => Promise<string | null>;
  onStartAnalysis: () => void;
  isAnalyzing: boolean;
}

const VideoUploader: React.FC<VideoUploaderProps> = ({
  activity,
  uploadedVideos,
  onVideoUpload,
  onStartAnalysis,
  isAnalyzing
}) => {
  const [uploadType, setUploadType] = useState<VideoType | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [validationWarnings, setValidationWarnings] = useState<Record<string, string>>({});
  // Removed isValidating state since we're simplifying the validation flow
  const [uploadedFiles, setUploadedFiles] = useState<Record<string, File>>({});

  // We'll keep validation only during upload, not before analysis
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (!uploadType || acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    setUploadError(null);

    try {
      console.log(`Processing dropped file: ${file.name}, type: ${file.type}, size: ${file.size} bytes`);

      // Validate video format before uploading
      console.log('Validating video format...');
      const validationResult = await validateVideoFormat(file);

      if (!validationResult.isValid) {
        console.error('Video validation failed:', validationResult.error);
        setUploadError(validationResult.error);
        return;
      }

      console.log('Video validation successful');

      // Store the file for later validation
      setUploadedFiles(prev => ({
        ...prev,
        [uploadType]: file
      }));

      // Store any warnings
      if (validationResult.warningMessage) {
        setValidationWarnings(prev => ({
          ...prev,
          [uploadType]: validationResult.warningMessage
        }));
      } else {
        // Clear any previous warnings for this type
        setValidationWarnings(prev => {
          const newWarnings = { ...prev };
          delete newWarnings[uploadType];
          return newWarnings;
        });
      }

      setIsUploading(true);
      const result = await onVideoUpload(uploadType, file);

      if (result) {
        console.log(`Upload successful, URL: ${result}`);
        setIsUploading(false);
        setUploadType(null);
      } else {
        console.error('Upload failed - no URL returned');
        setUploadError('Upload failed. Please try again with a different video file.');
        setIsUploading(false);
      }
    } catch (error) {
      console.error('Error in video upload process:', error);
      setUploadError('An error occurred while processing the video. Please try again.');
      setIsUploading(false);
    }
  }, [uploadType, onVideoUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'video/mp4': ['.mp4'],
      'video/quicktime': ['.mov'],
      'video/x-msvideo': ['.avi'],
      'video/webm': ['.webm']
    },
    disabled: !uploadType || isUploading,
    maxFiles: 1,
    maxSize: 100 * 1024 * 1024 // 100MB max file size
  });

  const handleRemoveVideo = (type: VideoType) => {
    // Create a new object without the removed video
    const newUploadedVideos = { ...uploadedVideos };
    delete newUploadedVideos[type];

    // Remove from uploaded files
    const newUploadedFiles = { ...uploadedFiles };
    delete newUploadedFiles[type];
    setUploadedFiles(newUploadedFiles);

    // Remove any warnings
    const newWarnings = { ...validationWarnings };
    delete newWarnings[type];
    setValidationWarnings(newWarnings);

    // Update the parent component's state by calling onVideoUpload with null
    onVideoUpload(type, null as any);
  };

  // Handle start analysis without additional validation
  const handleStartAnalysis = () => {
    // Just start the analysis - videos were already validated during upload
    onStartAnalysis();
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="analysis-container bg-white dark:bg-gray-800 p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-6">
          Upload {activity.charAt(0).toUpperCase() + activity.slice(1)} Videos
        </h2>

        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <VideoUploadBox
            title="Side View"
            description={`Upload a 10-second video of your ${activity} from the side`}
            isUploaded={!!uploadedVideos.side}
            videoUrl={uploadedVideos.side}
            onStartUpload={() => setUploadType('side')}
            onRemove={() => handleRemoveVideo('side')}
            uploadType={uploadType}
            currentType="side"
            warning={validationWarnings.side}
          />

          <VideoUploadBox
            title="Rear View"
            description={`Upload a 10-second video of your ${activity} from behind`}
            isUploaded={!!uploadedVideos.rear}
            videoUrl={uploadedVideos.rear}
            onStartUpload={() => setUploadType('rear')}
            onRemove={() => handleRemoveVideo('rear')}
            uploadType={uploadType}
            currentType="rear"
            warning={validationWarnings.rear}
          />
        </div>

        {uploadType && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            className={`upload-zone p-8 mb-6 text-center ${isDragActive ? 'active' : ''}`}
            {...getRootProps()}
          >
            <input {...getInputProps()} />

            {isUploading ? (
              <div className="flex flex-col items-center">
                <Loader2 className="animate-spin w-8 h-8 text-primary mb-3" />
                <p className="text-gray-600 dark:text-gray-300">Uploading video...</p>
              </div>
            ) : (
              <div className="flex flex-col items-center">
                <Upload className="w-12 h-12 text-gray-400 dark:text-gray-500 mb-3" />
                <p className="text-gray-600 dark:text-gray-300 mb-2">
                  Drag and drop your {uploadType} view video here, or click to select
                </p>
                <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                  <p>Requirements:</p>
                  <ul className="list-disc list-inside">
                    <li>Supported formats: MP4 with H.264 codec (recommended)</li>
                    <li>Also supported: MOV, AVI, WebM (may have compatibility issues)</li>
                    <li>Maximum 10 seconds duration</li>
                    <li>Maximum file size: 100MB</li>
                    <li>Good lighting and clear view</li>
                  </ul>
                </div>
              </div>
            )}
          </motion.div>
        )}

        {uploadError && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
          >
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 mr-2" />
              <div>
                <p className="text-red-700 dark:text-red-400 font-medium">Upload Error</p>
                <p className="text-red-600 dark:text-red-300 text-sm mt-1">{uploadError}</p>
                <p className="text-red-600 dark:text-red-300 text-sm mt-2">
                  Need help? Try using a free online video converter like:
                </p>
                <ul className="list-disc list-inside text-sm text-red-600 dark:text-red-300 mt-1">
                  <li>CloudConvert</li>
                  <li>Online-Convert</li>
                  <li>FFmpeg (for technical users)</li>
                </ul>
              </div>
            </div>
          </motion.div>
        )}

        {Object.keys(validationWarnings).length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg"
          >
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 mr-2" />
              <div>
                <p className="text-yellow-700 dark:text-yellow-400 font-medium">Video Compatibility Warning</p>
                <p className="text-yellow-600 dark:text-yellow-300 text-sm mt-1">
                  One or more of your videos may have compatibility issues:
                </p>
                <ul className="list-disc list-inside text-sm text-yellow-600 dark:text-yellow-300 mt-2">
                  {Object.entries(validationWarnings).map(([type, warning]) => (
                    <li key={type}>
                      <span className="font-medium">{type.charAt(0).toUpperCase() + type.slice(1)} view:</span> {warning}
                    </li>
                  ))}
                </ul>
                <p className="text-yellow-600 dark:text-yellow-300 text-sm mt-2">
                  For best results, convert your videos to MP4 format with H.264 codec before analysis.
                </p>
              </div>
            </div>
          </motion.div>
        )}

        <div className="flex justify-center">
          <button
            onClick={handleStartAnalysis}
            disabled={
              !uploadedVideos.side ||
              !uploadedVideos.rear ||
              isAnalyzing ||
              isUploading
            }
            className={`
              px-6 py-2 rounded-full font-medium flex items-center space-x-2
              ${
                isAnalyzing
                  ? 'bg-gray-400 dark:bg-gray-600 text-white cursor-not-allowed'
                  : !uploadedVideos.side || !uploadedVideos.rear || isUploading
                  ? 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  : 'bg-primary hover:bg-primary-dark text-white'
              }
              transition-colors
            `}
          >
            {isAnalyzing ? (
              <>
                <Loader2 className="animate-spin w-5 h-5 mr-2" />
                <span>Analyzing...</span>
              </>
            ) : (
              <>
                <Film className="w-5 h-5 mr-2" />
                <span>Start Analysis</span>
              </>
            )}
          </button>
        </div>
      </div>

      <div className="text-center text-sm text-gray-500 dark:text-gray-400">
        <p>Upload both side and rear view videos for complete analysis.</p>
        <p className="mt-1">Each video should be approximately 10 seconds in length.</p>
        <p className="mt-1 font-medium text-primary">📱 Portrait Videos Only - Hold your phone vertically when recording</p>
        <p className="mt-1 font-medium">For best results, use MP4 format with H.264 codec.</p>
      </div>
    </div>
  );
};

interface VideoUploadBoxProps {
  title: string;
  description: string;
  isUploaded: boolean;
  videoUrl: string;
  onStartUpload: () => void;
  onRemove: () => void;
  uploadType: VideoType | null;
  currentType: VideoType;
  warning?: string;
}

const VideoUploadBox: React.FC<VideoUploadBoxProps> = ({
  title,
  description,
  isUploaded,
  videoUrl,
  onStartUpload,
  onRemove,
  uploadType,
  currentType,
  warning
}) => {
  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
      {isUploaded ? (
        <div className="relative aspect-video bg-gray-100 dark:bg-gray-800">
          <video
            src={videoUrl}
            className="w-full h-full object-cover"
            controls
          />
          <div className="absolute top-2 right-2 flex space-x-2">
            <div className={`${warning ? 'bg-yellow-500' : 'bg-green-500'} text-white px-2 py-1 rounded-full text-xs flex items-center`}>
              {warning ? (
                <>
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  Warning
                </>
              ) : (
                <>
                  <CheckCircle2 className="w-3 h-3 mr-1" />
                  Uploaded
                </>
              )}
            </div>
            <button
              onClick={(e) => {
                e.preventDefault();
                onRemove();
              }}
              className="bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors"
              title="Remove video"
            >
              <Trash2 className="w-3 h-3" />
            </button>
          </div>

          {warning && (
            <div className="absolute bottom-2 left-2 right-2 bg-yellow-100 dark:bg-yellow-900 dark:bg-opacity-80 text-yellow-800 dark:text-yellow-200 text-xs p-2 rounded">
              <div className="flex items-start">
                <AlertTriangle className="w-3 h-3 mt-0.5 mr-1 flex-shrink-0" />
                <span>{warning}</span>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="aspect-video bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
          <Film className="w-12 h-12 text-gray-300 dark:text-gray-600" />
        </div>
      )}

      <div className="p-4">
        <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-1">{title}</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">{description}</p>

        <button
          onClick={onStartUpload}
          disabled={isUploaded || (uploadType !== null && uploadType !== currentType)}
          className={`
            w-full py-2 px-4 rounded-md text-sm font-medium
            ${isUploaded
              ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500 cursor-not-allowed'
              : uploadType !== null && uploadType !== currentType
                ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                : 'bg-primary bg-opacity-10 text-primary hover:bg-opacity-20 dark:bg-opacity-20 dark:hover:bg-opacity-30'}
            transition-colors
          `}
        >
          {isUploaded
            ? 'Uploaded'
            : `Upload ${title}`}
        </button>
      </div>
    </div>
  );
};

export default VideoUploader;