@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #35AE7C;
  --primary-light: #4BC48F;
  --primary-dark: #2A9A6C;
  --accent: #FF7846;
  --accent-light: #FF9A74;
  --accent-dark: #E36538;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  @apply bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100;
}

.analysis-container {
  border-radius: 16px;
  box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1);
}

.metric-card {
  border-radius: 12px;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
}

.progress-bar {
  height: 8px;
  border-radius: 4px;
  background: var(--gray-200);
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease-out;
}

.progress-bar-fill.good {
  background: var(--primary);
}

.progress-bar-fill.warning {
  background: var(--accent);
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.skeleton-node {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.skeleton-line {
  height: 2px;
  transform-origin: left center;
  animation: pulse 2s infinite;
}

.angle-label {
  background-color: var(--accent);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.video-controls {
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.7);
}

.upload-zone {
  border: 2px dashed var(--gray-300);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.upload-zone.active {
  border-color: var(--primary);
  background-color: rgba(53, 174, 124, 0.05);
}