/**
 * UPDATED APP.TSX WITH LIVE 3D ANALYSIS INTEGRATION
 * 
 * This integrates the new live 3D analysis system with the existing app structure,
 * removing the canvas-based video processing in favor of real-time SVG overlays.
 */

import { useState } from 'react';
import Header from './components/Header';
import VideoUploader from './components/LiveVideoUploader'; // Live 3D video uploader with configuration panel
import LiveAnalysisDisplay from './components/LiveAnalysisDisplay'; // New live analysis component
import './utils/SystemTester'; // Load system testing utilities
import DataManagementDashboard from './components/DataManagementDashboard';
import { ActivityType, VideoType, AnalysisResults } from './types';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;
const supabase = createClient(supabaseUrl, supabaseKey);

function App() {
  const [selectedActivity, setSelectedActivity] = useState<ActivityType>('running');
  const [uploadedVideos, setUploadedVideos] = useState<Record<string, string>>({});
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<AnalysisResults | null>(null);
  const [currentView, setCurrentView] = useState<'upload' | 'analysis' | 'data-management'>('upload');

  const handleVideoUpload = async (videoType: VideoType, file: File | null) => {
    try {
      if (!file) {
        // If file is null, remove the video
        setUploadedVideos(prev => {
          const newVideos = { ...prev };
          delete newVideos[videoType];
          return newVideos;
        });
        return null;
      }

      // Get file extension from the file type or name
      let fileExtension = '.mp4'; // Default extension
      let contentType = file.type || 'video/mp4';

      // Special handling for iPhone videos
      const isIPhoneVideo = file.name.toLowerCase().endsWith('.mov') ||
                           file.type === 'video/quicktime' ||
                           (file.name.includes('IMG_') && file.name.includes('.MOV'));

      if (isIPhoneVideo) {
        console.log('Detected iPhone video, using special handling');
        fileExtension = '.mov';
        contentType = 'video/quicktime';
      } else if (file.type) {
        // Extract extension from MIME type
        if (file.type === 'video/mp4') fileExtension = '.mp4';
        else if (file.type === 'video/quicktime') fileExtension = '.mov';
        else if (file.type === 'video/x-msvideo') fileExtension = '.avi';
        else if (file.type === 'video/webm') fileExtension = '.webm';
        // Fallback to extracting from filename if available
        else if (file.name && file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.substring(file.name.lastIndexOf('.'));
        }
      }

      const timestamp = new Date().getTime();
      // Use the correct bucket folder structure
      const folderPath = `${selectedActivity}-${videoType}`;
      const filePath = `${folderPath}/video_${timestamp}${fileExtension}`;

      console.log(`Uploading video with type: ${file.type}, extension: ${fileExtension}, contentType: ${contentType}`);

      const { error } = await supabase.storage
        .from('videos')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
          contentType: contentType // Use our determined content type
        });

      if (error) {
        console.error('Supabase upload error:', error);
        throw error;
      }

      // Get the public URL for the uploaded video
      const { data: { publicUrl } } = supabase.storage
        .from('videos')
        .getPublicUrl(filePath);

      console.log(`Video uploaded successfully. Public URL: ${publicUrl}`);

      setUploadedVideos(prev => ({
        ...prev,
        [videoType]: publicUrl
      }));

      return publicUrl;
    } catch (error) {
      console.error('Error uploading video:', error);
      return null;
    }
  };

  /**
   * CRITICAL FIX: Simplified start analysis for live 3D system
   * No more canvas-based video processing - just switch to live analysis view
   */
  const startAnalysis = async () => {
    console.log('Starting live 3D analysis with videos:', uploadedVideos);

    if (!uploadedVideos.side) {
      alert('Please upload a side view video before analyzing');
      return;
    }

    setIsAnalyzing(true);

    try {
      console.log('🎯 Starting live 3D analysis system...');

      // Get 3D configuration from global window object (set by VideoUploader)
      const globalConfig = (window as any).poseAnalysisConfig;
      
      console.log('Live 3D Analysis Configuration:', {
        userHeightMeters: globalConfig?.userHeightMeters || 1.78,
        overlayStyle: globalConfig?.overlayStyle || 'medical',
        processEveryFrame: globalConfig?.processEveryFrame || true
      });

      // Create analysis results for live 3D system
      // Note: The live system focuses on real-time analysis rather than pre-computed metrics
      const analysisResults: AnalysisResults = {
        overallScore: 0, // Will be calculated in real-time
        metrics: {
          stride: {
            score: 0,
            label: 'Stride Analysis',
            value: 'Live Analysis',
            notes: 'Real-time stride analysis using 3D world coordinates'
          },
          posture: {
            score: 0,
            label: 'Posture Analysis',
            value: 'Live Tracking',
            notes: 'Height-calibrated 3D pose tracking with real-world measurements'
          },
          footStrike: {
            score: 0,
            label: 'Foot Strike',
            value: 'Live Detection',
            notes: 'Real-time foot strike analysis with depth information'
          },
          kneeAngle: {
            score: 0,
            label: 'Knee Flexion',
            value: 'Live Measurement',
            notes: 'Real-time angle measurements using 3D coordinates'
          },
          pronation: {
            score: 0,
            label: 'Pronation',
            value: 'Live Analysis',
            notes: 'Advanced 3D foot mechanics analysis'
          }
        },
        jointAngles: {
          knee: 0, // Will be updated in real-time
          ankle: 0,
          hip: 0,
        }
      };

      console.log('✅ Live 3D analysis system ready');

      // Set results and change view
      setAnalysisResults(analysisResults);
      setCurrentView('analysis');
      setIsAnalyzing(false);

    } catch (error) {
      console.error('Error during live analysis setup:', error);
      setIsAnalyzing(false);

      // Show user-friendly error message
      alert(`Analysis setup failed: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`);
    }
  };

  const resetAnalysis = () => {
    setUploadedVideos({});
    setAnalysisResults(null);
    setCurrentView('upload');
    
    // Clear global 3D configuration
    delete (window as any).poseAnalysisConfig;
    
    console.log('🔄 Analysis reset - returning to upload view');
  };

  const handleViewChange = (view: 'upload' | 'analysis' | 'data-management') => {
    setCurrentView(view);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      <Header
        activity={selectedActivity}
        onActivityChange={setSelectedActivity}
        onReset={resetAnalysis}
        currentView={currentView}
        onViewChange={handleViewChange}
      />

      <main className="flex-1 container mx-auto px-4 py-8">
        {currentView === 'upload' ? (
          <VideoUploader
            activity={selectedActivity}
            onVideoUpload={handleVideoUpload}
            uploadedVideos={uploadedVideos}
            onStartAnalysis={startAnalysis}
            isAnalyzing={isAnalyzing}
          />
        ) : currentView === 'analysis' ? (
          <LiveAnalysisDisplay
            activity={selectedActivity}
            results={analysisResults}
            uploadedVideos={uploadedVideos}
          />
        ) : currentView === 'data-management' ? (
          <DataManagementDashboard />
        ) : null}
      </main>
    </div>
  );
}

export default App;