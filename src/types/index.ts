export type ActivityType = 'running' | 'cycling';
export type VideoType = 'side' | 'rear';

export interface VideoFile {
  file: File;
  preview: string;
}

export interface MetricData {
  score: number;
  label: string;
  value: string;
  notes: string;
}

export interface AnalysisResults {
  overallScore: number;
  metrics: Record<string, MetricData>;
  jointAngles: Record<string, number>;
  sessionId?: string;
  videoId?: string;
}