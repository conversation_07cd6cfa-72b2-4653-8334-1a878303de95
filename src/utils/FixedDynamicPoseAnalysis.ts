/**
 * FIXED DYNAMIC POSE ANALYSIS WITH ROI TRACKING
 * 
 * This implements the missing ROI tracking and dynamic scaling that the original
 * implementation was lacking. The key fixes:
 * 
 * 1. Dynamic ROI tracking (missing from original)
 * 2. Real-time coordinate center calculation
 * 3. Dynamic scaling based on detection size
 * 4. Proper 3D world landmark integration
 * 5. Live coordinate mapping instead of static database lookup
 */

import * as poseDetection from '@tensorflow-models/pose-detection';
import * as tf from '@tensorflow/tfjs';
import '@tensorflow/tfjs-backend-webgl';

// Enhanced interfaces for dynamic tracking
export interface DynamicPoseData {
  centerOfMass: { x: number; y: number };
  boundingBox: { x: number; y: number; width: number; height: number };
  scale: number;
  confidence: number;
  roi: { x: number; y: number; width: number; height: number } | null;
}

export interface RealTimeSkeleton {
  keypoints: { [key: string]: { x: number; y: number; confidence: number } };
  dynamicData: DynamicPoseData;
  timestamp: number;
}

// CRITICAL FIX: ROI Tracking Implementation
class DynamicROITracker {
  private previousROI: { x: number; y: number; width: number; height: number } | null = null;
  private framesSinceDetection = 0;
  private readonly MAX_FRAMES_WITHOUT_DETECTION = 5;

  calculateROIFromKeypoints(keypoints: any[]): { x: number; y: number; width: number; height: number } {
    if (!keypoints || keypoints.length === 0) return { x: 0, y: 0, width: 100, height: 100 };

    const validKeypoints = keypoints.filter(kp => kp.score > 0.3);
    if (validKeypoints.length === 0) return { x: 0, y: 0, width: 100, height: 100 };

    const xs = validKeypoints.map(kp => kp.x);
    const ys = validKeypoints.map(kp => kp.y);

    const minX = Math.min(...xs);
    const maxX = Math.max(...xs);
    const minY = Math.min(...ys);
    const maxY = Math.max(...ys);

    // Add padding for better tracking
    const padding = 0.2;
    const width = maxX - minX;
    const height = maxY - minY;
    
    const roi = {
      x: Math.max(0, minX - width * padding),
      y: Math.max(0, minY - height * padding),
      width: width * (1 + 2 * padding),
      height: height * (1 + 2 * padding)
    };

    this.previousROI = roi;
    this.framesSinceDetection = 0;
    
    return roi;
  }

  updateROI(hasDetection: boolean): { x: number; y: number; width: number; height: number } | null {
    if (hasDetection) {
      this.framesSinceDetection = 0;
      return this.previousROI;
    } else {
      this.framesSinceDetection++;
      
      if (this.framesSinceDetection > this.MAX_FRAMES_WITHOUT_DETECTION) {
        this.previousROI = null;
        return null;
      }
      
      return this.previousROI;
    }
  }

  reset() {
    this.previousROI = null;
    this.framesSinceDetection = 0;
  }
}

// CRITICAL FIX: Dynamic Center of Mass Calculator
class DynamicCenterCalculator {
  calculateCenterOfMass(keypoints: any[]): { x: number; y: number; confidence: number } {
    // Prioritize core body keypoints for center calculation
    const coreKeypoints = [
      'left_shoulder', 'right_shoulder',
      'left_hip', 'right_hip',
      'nose'
    ];

    const validCoreKeypoints = keypoints.filter(kp => 
      coreKeypoints.includes(kp.name) && kp.score > 0.5
    );

    if (validCoreKeypoints.length === 0) {
      // Fallback to all valid keypoints
      const fallbackKeypoints = keypoints.filter(kp => kp.score > 0.3);
      if (fallbackKeypoints.length === 0) {
        return { x: 50, y: 50, confidence: 0 };
      }

      const centerX = fallbackKeypoints.reduce((sum, kp) => sum + kp.x, 0) / fallbackKeypoints.length;
      const centerY = fallbackKeypoints.reduce((sum, kp) => sum + kp.y, 0) / fallbackKeypoints.length;
      const avgConfidence = fallbackKeypoints.reduce((sum, kp) => sum + kp.score, 0) / fallbackKeypoints.length;

      return { x: centerX, y: centerY, confidence: avgConfidence };
    }

    // Calculate weighted center of mass using core keypoints
    let totalWeight = 0;
    let weightedX = 0;
    let weightedY = 0;

    validCoreKeypoints.forEach(kp => {
      const weight = kp.score;
      weightedX += kp.x * weight;
      weightedY += kp.y * weight;
      totalWeight += weight;
    });

    const centerX = weightedX / totalWeight;
    const centerY = weightedY / totalWeight;
    const avgConfidence = totalWeight / validCoreKeypoints.length;

    return { x: centerX, y: centerY, confidence: avgConfidence };
  }
}

// CRITICAL FIX: Dynamic Scale Calculator
class DynamicScaleCalculator {
  private previousScale = 1.0;
  private readonly SMOOTHING_FACTOR = 0.3;

  calculateDynamicScale(keypoints: any[], _videoWidth: number, videoHeight: number): number {
    // Method 1: Use shoulder-to-hip distance
    const leftShoulder = keypoints.find(kp => kp.name === 'left_shoulder' && kp.score > 0.5);
    const rightShoulder = keypoints.find(kp => kp.name === 'right_shoulder' && kp.score > 0.5);
    const leftHip = keypoints.find(kp => kp.name === 'left_hip' && kp.score > 0.5);
    const rightHip = keypoints.find(kp => kp.name === 'right_hip' && kp.score > 0.5);

    if (leftShoulder && rightShoulder && leftHip && rightHip) {
      const shoulderMidpoint = {
        x: (leftShoulder.x + rightShoulder.x) / 2,
        y: (leftShoulder.y + rightShoulder.y) / 2
      };
      const hipMidpoint = {
        x: (leftHip.x + rightHip.x) / 2,
        y: (leftHip.y + rightHip.y) / 2
      };

      const torsoLength = Math.sqrt(
        Math.pow(shoulderMidpoint.x - hipMidpoint.x, 2) + 
        Math.pow(shoulderMidpoint.y - hipMidpoint.y, 2)
      );

      // Expected torso length as percentage of video height
      const expectedTorsoLength = videoHeight * 0.25; // ~25% of video height
      const calculatedScale = torsoLength / expectedTorsoLength;
      
      // Apply smoothing to prevent jitter
      const smoothedScale = this.previousScale * (1 - this.SMOOTHING_FACTOR) + 
                           calculatedScale * this.SMOOTHING_FACTOR;
      
      // Clamp to reasonable bounds
      const clampedScale = Math.max(0.5, Math.min(2.0, smoothedScale));
      this.previousScale = clampedScale;
      
      return clampedScale;
    }

    // Method 2: Use bounding box size
    const validKeypoints = keypoints.filter(kp => kp.score > 0.3);
    if (validKeypoints.length >= 5) {
      const ys = validKeypoints.map(kp => kp.y);
      const boundingBoxHeight = Math.max(...ys) - Math.min(...ys);
      
      const expectedBodyHeight = videoHeight * 0.7; // ~70% of video height
      const calculatedScale = boundingBoxHeight / expectedBodyHeight;
      
      const smoothedScale = this.previousScale * (1 - this.SMOOTHING_FACTOR) + 
                           calculatedScale * this.SMOOTHING_FACTOR;
      
      const clampedScale = Math.max(0.5, Math.min(2.0, smoothedScale));
      this.previousScale = clampedScale;
      
      return clampedScale;
    }

    return this.previousScale;
  }

  reset() {
    this.previousScale = 1.0;
  }
}

// Enhanced detector with ROI tracking
let detector: poseDetection.PoseDetector | null = null;
const roiTracker = new DynamicROITracker();
const centerCalculator = new DynamicCenterCalculator();
const scaleCalculator = new DynamicScaleCalculator();

export async function initializeDynamicPoseDetection(): Promise<poseDetection.PoseDetector> {
  if (detector) return detector;

  try {
    await tf.ready();
    await tf.setBackend('webgl');

    // CRITICAL FIX: Enhanced BlazePose configuration for 3D tracking
    detector = await poseDetection.createDetector(
      poseDetection.SupportedModels.BlazePose,
      {
        runtime: 'tfjs',
        modelType: 'full',
        enableSmoothing: true,
        enableSegmentation: false
      }
    );

    console.log('✅ Dynamic BlazePose detector initialized with ROI tracking');
    return detector;
  } catch (error) {
    console.error('Failed to initialize dynamic pose detection:', error);
    throw error;
  }
}

// CRITICAL FIX: Real-time dynamic pose analysis
export async function analyzePoseDynamic(
  source: HTMLVideoElement | HTMLCanvasElement,
  currentTime: number
): Promise<RealTimeSkeleton> {
  if (!detector) {
    detector = await initializeDynamicPoseDetection();
  }

  const timestamp = currentTime * 1000000; // Convert to microseconds for BlazePose

  try {
    // Get video dimensions
    const videoWidth = source instanceof HTMLVideoElement ? source.videoWidth : source.width;
    const videoHeight = source instanceof HTMLVideoElement ? source.videoHeight : source.height;

    // CRITICAL: Use timestamp for temporal smoothing
    const poses = await detector.estimatePoses(
      source,
      {
        maxPoses: 1,
        flipHorizontal: false
      },
      timestamp
    );

    if (poses.length === 0) {
      // No detection - update ROI tracker
      roiTracker.updateROI(false);
      
      return {
        keypoints: {},
        dynamicData: {
          centerOfMass: { x: 50, y: 50 },
          boundingBox: { x: 0, y: 0, width: 100, height: 100 },
          scale: 1.0,
          confidence: 0,
          roi: null
        },
        timestamp: currentTime
      };
    }

    const pose = poses[0];
    const keypoints = pose.keypoints;

    // CRITICAL FIX: Calculate dynamic center of mass
    const centerOfMass = centerCalculator.calculateCenterOfMass(keypoints);

    // CRITICAL FIX: Update ROI tracking
    const roi = roiTracker.calculateROIFromKeypoints(keypoints);

    // CRITICAL FIX: Calculate dynamic scale
    const scale = scaleCalculator.calculateDynamicScale(keypoints, videoWidth, videoHeight);

    // Normalize coordinates to 0-100 percentage
    const normalizedKeypoints: { [key: string]: { x: number; y: number; confidence: number } } = {};
    
    keypoints.forEach((kp: any) => {
      if (kp?.score > 0.1 && kp?.name) {
        normalizedKeypoints[kp.name] = {
          x: (kp.x / videoWidth) * 100,
          y: (kp.y / videoHeight) * 100,
          confidence: kp.score
        };
      }
    });

    // Calculate bounding box
    const validKeypoints = keypoints.filter((kp: any) => kp?.score > 0.3);
    let boundingBox = { x: 0, y: 0, width: 100, height: 100 };
    
    if (validKeypoints.length > 0) {
      const xs = validKeypoints.map(kp => (kp.x / videoWidth) * 100);
      const ys = validKeypoints.map(kp => (kp.y / videoHeight) * 100);
      
      boundingBox = {
        x: Math.min(...xs),
        y: Math.min(...ys),
        width: Math.max(...xs) - Math.min(...xs),
        height: Math.max(...ys) - Math.min(...ys)
      };
    }

    console.log(`🎯 Dynamic tracking - Center: (${centerOfMass.x.toFixed(1)}, ${centerOfMass.y.toFixed(1)}), Scale: ${scale.toFixed(2)}`);

    return {
      keypoints: normalizedKeypoints,
      dynamicData: {
        centerOfMass: {
          x: (centerOfMass.x / videoWidth) * 100,
          y: (centerOfMass.y / videoHeight) * 100
        },
        boundingBox,
        scale,
        confidence: centerOfMass.confidence,
        roi: {
          x: (roi.x / videoWidth) * 100,
          y: (roi.y / videoHeight) * 100,
          width: (roi.width / videoWidth) * 100,
          height: (roi.height / videoHeight) * 100
        }
      },
      timestamp: currentTime
    };
  } catch (error) {
    console.error('Dynamic pose analysis failed:', error);
    throw error;
  }
}

// Reset function for new videos
export function resetDynamicTracking() {
  roiTracker.reset();
  scaleCalculator.reset();
  console.log('🔄 Dynamic tracking reset for new video');
}