import { createClient } from '@supabase/supabase-js';
import { ActivityType, VideoType } from '../types';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl) {
  throw new Error('VITE_SUPABASE_URL environment variable is not defined');
}

if (!supabaseKey) {
  throw new Error('VITE_SUPABASE_ANON_KEY environment variable is not defined');
}

const supabase = createClient(supabaseUrl, supabaseKey);

export const processVideo = async (
  videoUrl: string, 
  activity: ActivityType, 
  viewType: VideoType
) => {
  try {
    const response = await fetch(`${supabaseUrl}/functions/v1/process-video`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        videoUrl,
        activity,
        viewType,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Processing result:', data);
    return data;
  } catch (error) {
    console.error('Error in video processing:', error);
    throw error;
  }
};