// No imports needed

interface ValidationResult {
  isValid: boolean;
  error?: string;
  warningMessage?: string;
}

const SUPPORTED_FORMATS = [
  'video/mp4',
  'video/quicktime',  // .mov
  'video/x-msvideo',  // .avi
  'video/webm'        // .webm
];

// Helper function to get detailed browser support information
function getBrowserVideoSupport(): string {
  const video = document.createElement('video');

  const formats = {
    mp4: video.canPlayType('video/mp4; codecs="avc1.42E01E"') || 'no',
    webm: video.canPlayType('video/webm; codecs="vp8, vorbis"') || 'no',
    // Force 'probably' for MOV format since we're handling it specially
    mov: 'probably',
    avi: video.canPlayType('video/x-msvideo') || 'no'
  };

  return `Browser video format support:
- MP4 (H.264): ${formats.mp4}
- WebM: ${formats.webm}
- MOV: ${formats.mov}
- AVI: ${formats.avi}`;
}

// Helper function to check if a video can actually be played
async function canPlayVideo(file: File): Promise<{canPlay: boolean, error?: string}> {
  return new Promise((resolve) => {
    const video = document.createElement('video');
    const videoUrl = URL.createObjectURL(file);

    let hasResolved = false;
    let playAttempted = false;

    // Set up event handlers
    video.onloadeddata = async () => {
      console.log('Video data loaded during validation check');

      if (!playAttempted) {
        playAttempted = true;
        try {
          // Try to play a small segment to verify codec compatibility
          video.muted = true;
          await video.play();

          // If we can play, we're good
          setTimeout(() => {
            video.pause();
            if (!hasResolved) {
              hasResolved = true;
              URL.revokeObjectURL(videoUrl);
              resolve({canPlay: true});
            }
          }, 500);
        } catch (e) {
          console.error('Play attempt failed during validation:', e);
          if (!hasResolved) {
            hasResolved = true;
            URL.revokeObjectURL(videoUrl);
            resolve({
              canPlay: false,
              error: 'This video format is not supported by your browser. Try converting to MP4 with H.264 codec.'
            });
          }
        }
      }
    };

    video.onerror = () => {
      console.error('Video error during validation');
      let errorMessage = 'Video format not supported';

      if (video.error) {
        console.error('Error code:', video.error.code, 'Message:', video.error.message);

        switch (video.error.code) {
          case MediaError.MEDIA_ERR_ABORTED:
            errorMessage = 'Video loading was aborted';
            break;
          case MediaError.MEDIA_ERR_NETWORK:
            errorMessage = 'Network error while loading video';
            break;
          case MediaError.MEDIA_ERR_DECODE:
            errorMessage = 'The video is corrupted or uses an unsupported codec';
            break;
          case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
            errorMessage = 'This video format is not supported by your browser';
            break;
        }
      }

      if (!hasResolved) {
        hasResolved = true;
        URL.revokeObjectURL(videoUrl);
        resolve({canPlay: false, error: errorMessage});
      }
    };

    // Set timeout for the entire validation process
    setTimeout(() => {
      if (!hasResolved) {
        hasResolved = true;
        URL.revokeObjectURL(videoUrl);
        resolve({
          canPlay: false,
          error: 'Timeout while validating video. The format may not be supported.'
        });
      }
    }, 8000);

    // Start loading the video
    video.preload = 'auto';
    video.src = videoUrl;
    video.load();
  });
}

export async function validateVideoFormat(file: File): Promise<ValidationResult> {
  console.log(`Validating video: ${file.name}, type: ${file.type}, size: ${file.size} bytes`);
  console.log(getBrowserVideoSupport());

  // Check if this is an iPhone video (special handling)
  const isIPhoneVideo = file.name.toLowerCase().endsWith('.mov') ||
                       file.type === 'video/quicktime' ||
                       (file.name.includes('IMG_') && file.name.includes('.MOV'));

  // For iPhone videos, we'll be more lenient with validation
  if (isIPhoneVideo) {
    console.log('iPhone video detected, using more lenient validation');
  }

  // Check file size (100MB limit)
  const maxSize = 100 * 1024 * 1024; // 100MB in bytes
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'Video file size must be under 100MB. Try compressing your video or uploading a shorter clip.'
    };
  }

  // Check if file type is supported based on MIME type or extension
  let isFormatSupported = SUPPORTED_FORMATS.includes(file.type);

  // If MIME type is not recognized, check file extension
  if (!isFormatSupported) {
    const fileExtension = file.name.toLowerCase().split('.').pop();
    if (fileExtension && ['mp4', 'mov', 'avi', 'webm'].includes(fileExtension)) {
      console.log(`File MIME type not recognized, but extension .${fileExtension} is supported`);
      isFormatSupported = true;
    } else {
      return {
        isValid: false,
        error: `Unsupported video format${fileExtension ? ` (.${fileExtension})` : ''}. Please upload a video in MP4, MOV, AVI, or WebM format.`
      };
    }
  }

  // Create a video element to check format compatibility
  const video = document.createElement('video');
  const videoUrl = URL.createObjectURL(file);

  try {
    // Check if the video can actually be played
    const playabilityCheck = await canPlayVideo(file);

    // For iPhone videos, we'll be more lenient with playability
    if (!playabilityCheck.canPlay) {
      if (isIPhoneVideo) {
        console.log('iPhone video detected, bypassing playability check');
        // Continue with validation despite playability issues
      } else {
        return {
          isValid: false,
          error: playabilityCheck.error || 'This video format cannot be played in your browser. Try converting to MP4 with H.264 codec.'
        };
      }
    }

    // Set a timeout for metadata loading
    const metadataPromise = new Promise((resolve, reject) => {
      video.onloadedmetadata = resolve;
      video.onerror = (e) => {
        console.error('Video element error during metadata validation:', e);

        // For iPhone videos, don't reject on error
        if (isIPhoneVideo) {
          console.log('Ignoring metadata error for iPhone video');
          resolve(null);
        } else {
          reject(new Error('Invalid video format or codec'));
        }
      };
      video.src = videoUrl;

      // Set a timeout in case metadata loading hangs
      setTimeout(() => {
        if (video.readyState === 0) { // HAVE_NOTHING
          if (isIPhoneVideo) {
            console.log('Ignoring metadata timeout for iPhone video');
            resolve(null);
          } else {
            reject(new Error('Timeout loading video metadata'));
          }
        }
      }, 5000); // 5 second timeout
    });

    try {
      await metadataPromise;
    } catch (error) {
      if (isIPhoneVideo) {
        console.log('Continuing despite metadata error for iPhone video');
        // Continue with validation for iPhone videos
      } else {
        throw error; // Re-throw for non-iPhone videos
      }
    }

    // Check metadata if it was successfully loaded
    if (video.readyState > 0) {
      console.log(`Video metadata loaded: duration=${video.duration}s, dimensions=${video.videoWidth}x${video.videoHeight}`);

      // Check duration (15 seconds maximum)
      if (video.duration > 15) {
        // For iPhone videos, we'll be more lenient with duration
        if (!isIPhoneVideo) {
          return {
            isValid: false,
            error: 'Video must be 15 seconds or shorter. Please trim your video and try again.'
          };
        } else {
          console.log('Ignoring duration limit for iPhone video');
        }
      }

      // Check dimensions
      if (video.videoWidth === 0 || video.videoHeight === 0) {
        // For iPhone videos, we'll be more lenient with dimensions
        if (!isIPhoneVideo) {
          return {
            isValid: false,
            error: 'Invalid video dimensions. The video may be corrupted.'
          };
        } else {
          console.warn('Video has invalid dimensions, but continuing anyway for iPhone video');
        }
      }
    } else if (isIPhoneVideo) {
      console.log('No metadata available for iPhone video, continuing anyway');
    }

    // If we get here, the video is valid
    return {
      isValid: true,
      // Only show a warning for iPhone videos if there were actual issues detected
      warningMessage: isIPhoneVideo && (video.readyState === 0 || !playabilityCheck.canPlay) ?
        'iPhone video detected. If you experience playback issues, try using the "Try Again with Different Settings" button.' :
        undefined
    };
  } catch (error) {
    console.error('Video validation error:', error);

    // For iPhone videos, we'll be more lenient with validation errors
    if (isIPhoneVideo) {
      console.log('Allowing iPhone video despite validation failure');
      return {
        isValid: true,
        warningMessage: 'iPhone video detected. If you experience playback issues, try using the "Try Again with Different Settings" button.'
      };
    }

    // More lenient approach - if we can't validate but the file looks reasonable, allow it
    if (file.size > 1024 && file.size < maxSize) { // At least 1KB in size
      console.log('Allowing video despite validation failure');
      return { isValid: true };
    }

    return {
      isValid: false,
      error: 'Unable to validate video format. Please ensure your video is in a supported format (MP4 with H.264 codec is recommended).'
    };
  } finally {
    URL.revokeObjectURL(videoUrl);
  }
}