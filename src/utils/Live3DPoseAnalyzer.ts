/**
 * LIVE 3D POSE ANALYZER WITH REAL-TIME SVG OVERLAY
 * 
 * This replaces the canvas-based video processing with a live SVG overlay system
 * that renders skeletal overlays in real-time on top of video elements.
 * 
 * Key Features:
 * - Real-time 3D pose detection using BlazePose
 * - Live SVG overlay rendering (no pre-processing)
 * - Correct 3D to 2D coordinate transformation
 * - Height-based scaling with real-world measurements
 * - Performance optimized for smooth playback
 */

import * as poseDetection from '@tensorflow-models/pose-detection';
import * as tf from '@tensorflow/tfjs';
import '@tensorflow/tfjs-backend-webgl';

// 3D Pose Analysis Configuration
export interface Live3DConfig {
  userHeightMeters: number;
  overlayStyle: 'medical' | 'athletic' | 'minimal';
  confidenceThreshold: number;
  smoothingFactor: number;
}

// Debug mode setup
declare global {
  interface Window {
    POSE_DEBUG?: boolean;
  }
}

// Debug mode utilities
export const enableDebugMode = (): void => {
  localStorage.setItem('pose-debug', 'true');
  window.POSE_DEBUG = true;
  console.log('🐛 Pose debug mode enabled');
};

export const isDebugMode = (): boolean => {
  return localStorage.getItem('pose-debug') === 'true' || window.POSE_DEBUG === true;
};

export const debugLog = (message: string, ...args: any[]): void => {
  if (isDebugMode()) {
    console.log(`[POSE_DEBUG] ${message}`, ...args);
  }
};

// 3D World Landmark (from BlazePose)
export interface WorldLandmark3D {
  x: number; // meters, relative to hip center
  y: number; // meters, relative to hip center
  z: number; // meters, depth from hip center
  score: number; // confidence score
  name: string;
}

// Real-time pose data with 3D information
export interface LivePoseData {
  timestamp: number;
  worldLandmarks: { [key: string]: WorldLandmark3D };
  screenCoordinates: { [key: string]: { x: number; y: number; z: number; confidence: number } };
  realWorldScale: number;
  heightCalibrated: boolean;
  detectionConfidence: number;
  jointAngles: { [key: string]: number };
  centerOfMass: { x: number; y: number; z: number };
}

// Progress tracking for analysis
export interface AnalysisProgress {
  currentTime: number;
  totalDuration: number;
  framesProcessed: number;
  averageConfidence: number;
  detectionRate: number;
}

/**
 * Live 3D Pose Analyzer Class
 * Handles real-time 3D pose detection and coordinate transformation
 */
export class Live3DPoseAnalyzer {
  private detector: poseDetection.PoseDetector | null = null;
  private isInitialized = false;
  private isInitializing = false; // ADD THIS
  private config: Live3DConfig;
  private previousPose: LivePoseData | null = null;
  
  // Constants for 3D coordinate transformation
  private readonly HIP_TO_HEAD_RATIO = 0.573; // Anatomical ratio
  private readonly WORLD_COORDINATE_RANGE = 2.0; // BlazePose 3D range: -1 to 1 meters

  constructor(config: Live3DConfig) {
    this.config = config;
    console.log('🎯 Live 3D Pose Analyzer initialized with config:', config);
  }

  /**
   * Initialize BlazePose 3D detector
   */
  async initialize(): Promise<void> {
    if (this.isInitialized || this.isInitializing) return;
    
    this.isInitializing = true; // SET FLAG
    
    try {
      console.log('🚀 Initializing BlazePose 3D detector...');
      
      // Ensure TensorFlow.js backend is ready
      await tf.ready();
      await tf.setBackend('webgl');
      
      // Wait for backend to be fully ready
      await tf.nextFrame();

      // Create BlazePose detector with optimal 3D configuration
      this.detector = await poseDetection.createDetector(
        poseDetection.SupportedModels.BlazePose,
        {
          runtime: 'tfjs',
          modelType: 'full',
          enableSmoothing: true,
          enableSegmentation: false
        }
      );

      this.isInitialized = true;
      console.log('✅ BlazePose 3D detector initialized successfully');
      
    } catch (error) {
      console.error('❌ Failed to initialize BlazePose 3D detector:', error);
      throw new Error(`BlazePose initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      this.isInitializing = false; // CLEAR FLAG
    }
  }


    
  /**
   * Analyze pose from video element at current time
   * This is the core method that extracts 3D pose data
   */
  async analyzePose(
    videoElement: HTMLVideoElement,
    currentTime?: number
  ): Promise<LivePoseData | null> {
    // Check both initialization flags
    if (!this.isInitialized || !this.detector) {
      console.warn('⚠️ Analyzer not ready - skipping frame');
      return null;
    }

    try {
      // Use current video time or provided timestamp
      const timestamp = currentTime ?? videoElement.currentTime;
      const timestampMicroseconds = timestamp * 1000000; // BlazePose expects microseconds

      // Run pose detection with temporal smoothing
      const poses = await this.detector.estimatePoses(
        videoElement,
        {
          maxPoses: 1,
          flipHorizontal: false
        },
        timestampMicroseconds
      );

      if (poses.length === 0) {
        console.log(`⚠️ No pose detected at ${timestamp.toFixed(3)}s`);
        return null;
      }

      const pose = poses[0];

      // CRITICAL: Access 3D world landmarks from BlazePose
      if (!pose.keypoints3D || pose.keypoints3D.length === 0) {
        console.error('❌ No 3D keypoints available from BlazePose');
        return null;
      }

      console.log(`🌐 3D pose detected at ${timestamp.toFixed(3)}s - ${pose.keypoints3D.length} 3D keypoints`);

      // Convert BlazePose 3D keypoints to our format
      const worldLandmarks: { [key: string]: WorldLandmark3D } = {};
      
      pose.keypoints3D.forEach((kp: any) => {
        if (kp?.score > this.config.confidenceThreshold && kp?.name) {
          worldLandmarks[kp.name] = {
            x: kp.x || 0, // Already in meters relative to hip center
            y: kp.y || 0, // Already in meters relative to hip center
            z: kp.z || 0, // Already in meters depth
            score: kp.score || 0,
            name: kp.name
          };
        }
      });

      // Calculate real-world scale using height calibration
      const realWorldScale = this.calculateHeightBasedScale(worldLandmarks);
      
      // Transform 3D coordinates to screen coordinates
      const screenCoordinates = this.transformToScreenCoordinates(
        worldLandmarks,
        realWorldScale,
        videoElement.videoWidth,
        videoElement.videoHeight
      );

      // Calculate joint angles using 3D coordinates
      const jointAngles = this.calculate3DJointAngles(worldLandmarks);

      // Calculate 3D center of mass
      const centerOfMass = this.calculate3DCenterOfMass(worldLandmarks);

      const liveData: LivePoseData = {
        timestamp,
        worldLandmarks,
        screenCoordinates,
        realWorldScale,
        heightCalibrated: true,
        detectionConfidence: pose.score || 0,
        jointAngles,
        centerOfMass
      };

      // Apply temporal smoothing
      if (this.previousPose && this.config.smoothingFactor > 0) {
        this.applySmoothingFilter(liveData, this.previousPose);
      }

      this.previousPose = liveData;
      return liveData;

    } catch (error) {
      console.error(`❌ Pose analysis failed at ${videoElement.currentTime.toFixed(3)}s:`, error);
      return null;
    }
  }

  /**
   * CRITICAL FIX: Correct 3D to 2D coordinate transformation
   * This fixes the mathematical error in the original implementation
   */
  private transformToScreenCoordinates(
    worldLandmarks: { [key: string]: WorldLandmark3D },
    realWorldScale: number,
    videoWidth: number,
    videoHeight: number
  ): { [key: string]: { x: number; y: number; z: number; confidence: number } } {

  const screenCoords: { [key: string]: { x: number; y: number; z: number; confidence: number } } = {};

  console.log(`🔄 Transforming ${Object.keys(worldLandmarks).length} landmarks to normalized coords`);

  // Find hip center for relative positioning
  const leftHip = worldLandmarks['left_hip'];
  const rightHip = worldLandmarks['right_hip'];

  let hipCenterX = 0, hipCenterY = 0;
  if (leftHip && rightHip) {
    hipCenterX = (leftHip.x + rightHip.x) / 2;
    hipCenterY = (leftHip.y + rightHip.y) / 2;
  }

  Object.entries(worldLandmarks).forEach(([name, landmark]) => {
    // BlazePose 3D coords are in meters relative to hip center
    // Scale and center in 0-100 coordinate space

    const relativeX = (landmark.x - hipCenterX) * realWorldScale;
    const relativeY = (landmark.y - hipCenterY) * realWorldScale;

    // Convert to 0-100 with person centered at 50,50
    // Use 30 as scale factor for good visibility
    const normalizedX = 50 + (relativeX * 30);
    const normalizedY = 50 - (relativeY * 30); // Invert Y

    screenCoords[name] = {
      x: Math.max(0, Math.min(100, normalizedX)),
      y: Math.max(0, Math.min(100, normalizedY)),
      z: landmark.z * realWorldScale,
      confidence: landmark.score
    };

    // Debug key joints
    if (name === 'left_hip' || name === 'left_knee' || name === 'left_ankle') {
      console.log(`📍 ${name}: World(${landmark.x.toFixed(3)}, ${landmark.y.toFixed(3)}) → Normalized(${screenCoords[name].x.toFixed(1)}, ${screenCoords[name].y.toFixed(1)})`);
    }
  });

  return screenCoords;
  }

  /**
   * Calculate real-world scale using user height (5'10" = 1.78m)
   * This converts BlazePose normalized coordinates to actual measurements
   */
  private calculateHeightBasedScale(worldLandmarks: { [key: string]: WorldLandmark3D }): number {
    // Try to find head/neck and hip landmarks for height measurement
    const nose = worldLandmarks['nose'];
    const leftHip = worldLandmarks['left_hip'];
    const rightHip = worldLandmarks['right_hip'];

    if (!nose || (!leftHip && !rightHip)) {
      console.warn('⚠️ Insufficient landmarks for height calibration, using default scale');
      return 1.0;
    }

    // Calculate hip center (origin point for BlazePose 3D)
    const hipCenter = {
      x: leftHip && rightHip ? (leftHip.x + rightHip.x) / 2 : (leftHip?.x || rightHip?.x || 0),
      y: leftHip && rightHip ? (leftHip.y + rightHip.y) / 2 : (leftHip?.y || rightHip?.y || 0),
      z: leftHip && rightHip ? (leftHip.z + rightHip.z) / 2 : (leftHip?.z || rightHip?.z || 0)
    };

    // Calculate detected head-to-hip distance in BlazePose 3D coordinates
    const detectedHeadToHipDistance = Math.sqrt(
      Math.pow(nose.x - hipCenter.x, 2) +
      Math.pow(nose.y - hipCenter.y, 2) +
      Math.pow(nose.z - hipCenter.z, 2)
    );

    // Real-world head-to-hip distance based on user height
    const realWorldHeadToHipDistance = this.config.userHeightMeters * this.HIP_TO_HEAD_RATIO;

    // Calculate scale factor
    const scaleFactor = realWorldHeadToHipDistance / detectedHeadToHipDistance;

    console.log(`📏 Height calibration - Detected: ${detectedHeadToHipDistance.toFixed(3)}m, Real: ${realWorldHeadToHipDistance.toFixed(3)}m, Scale: ${scaleFactor.toFixed(3)}`);

    // Clamp to reasonable bounds
    return Math.max(0.5, Math.min(2.0, scaleFactor));
  }

  /**
   * Calculate 3D joint angles using world landmarks
   */
  private calculate3DJointAngles(worldLandmarks: { [key: string]: WorldLandmark3D }): { [key: string]: number } {
    const angles: { [key: string]: number } = {};

    // Helper function to calculate 3D angle between three points
    const calculate3DAngle = (p1: WorldLandmark3D, vertex: WorldLandmark3D, p2: WorldLandmark3D): number => {
      const v1 = { x: p1.x - vertex.x, y: p1.y - vertex.y, z: p1.z - vertex.z };
      const v2 = { x: p2.x - vertex.x, y: p2.y - vertex.y, z: p2.z - vertex.z };

      const dot = v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
      const mag1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y + v1.z * v1.z);
      const mag2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y + v2.z * v2.z);

      if (mag1 === 0 || mag2 === 0) return 180;

      const cosAngle = Math.max(-1, Math.min(1, dot / (mag1 * mag2)));
      return Math.acos(cosAngle) * (180 / Math.PI);
    };

    // Calculate key joint angles for running analysis
    const leftHip = worldLandmarks['left_hip'];
    const leftKnee = worldLandmarks['left_knee'];
    const leftAnkle = worldLandmarks['left_ankle'];
    const leftShoulder = worldLandmarks['left_shoulder'];
    const leftElbow = worldLandmarks['left_elbow'];
    const leftWrist = worldLandmarks['left_wrist'];

    if (leftHip && leftKnee && leftAnkle) {
      angles.leftKnee = calculate3DAngle(leftHip, leftKnee, leftAnkle);
    }

    if (leftKnee && leftAnkle && worldLandmarks['left_foot_index']) {
      angles.leftAnkle = calculate3DAngle(leftKnee, leftAnkle, worldLandmarks['left_foot_index']);
    }

    if (leftShoulder && leftElbow && leftWrist) {
      angles.leftElbow = calculate3DAngle(leftShoulder, leftElbow, leftWrist);
    }

    // Right side angles
    const rightHip = worldLandmarks['right_hip'];
    const rightKnee = worldLandmarks['right_knee'];
    const rightAnkle = worldLandmarks['right_ankle'];
    const rightShoulder = worldLandmarks['right_shoulder'];
    const rightElbow = worldLandmarks['right_elbow'];
    const rightWrist = worldLandmarks['right_wrist'];

    if (rightHip && rightKnee && rightAnkle) {
      angles.rightKnee = calculate3DAngle(rightHip, rightKnee, rightAnkle);
    }

    if (rightKnee && rightAnkle && worldLandmarks['right_foot_index']) {
      angles.rightAnkle = calculate3DAngle(rightKnee, rightAnkle, worldLandmarks['right_foot_index']);
    }

    if (rightShoulder && rightElbow && rightWrist) {
      angles.rightElbow = calculate3DAngle(rightShoulder, rightElbow, rightWrist);
    }

    // Trunk angle (spine inclination)
    if (worldLandmarks['nose'] && leftHip && rightHip) {
      const hipCenter = {
        x: (leftHip.x + rightHip.x) / 2,
        y: (leftHip.y + rightHip.y) / 2,
        z: (leftHip.z + rightHip.z) / 2
      };
      
      // Calculate trunk inclination from vertical
      const trunkVector = {
        x: worldLandmarks['nose'].x - hipCenter.x,
        y: worldLandmarks['nose'].y - hipCenter.y,
        z: worldLandmarks['nose'].z - hipCenter.z
      };
      
      const verticalVector = { x: 0, y: 1, z: 0 };

      // Calculate angle between trunk vector and vertical vector
      const dot = trunkVector.x * verticalVector.x + trunkVector.y * verticalVector.y + trunkVector.z * verticalVector.z;
      const trunkMag = Math.sqrt(trunkVector.x * trunkVector.x + trunkVector.y * trunkVector.y + trunkVector.z * trunkVector.z);
      const verticalMag = Math.sqrt(verticalVector.x * verticalVector.x + verticalVector.y * verticalVector.y + verticalVector.z * verticalVector.z);

      if (trunkMag > 0 && verticalMag > 0) {
        const cosAngle = Math.max(-1, Math.min(1, dot / (trunkMag * verticalMag)));
        angles.trunkInclination = Math.acos(Math.abs(cosAngle)) * (180 / Math.PI);
      }
    }

    return angles;
  }

  /**
   * Calculate 3D center of mass
   */
  private calculate3DCenterOfMass(worldLandmarks: { [key: string]: WorldLandmark3D }): { x: number; y: number; z: number } {
    const corePoints = ['left_hip', 'right_hip', 'left_shoulder', 'right_shoulder'];
    const validPoints = corePoints
      .map(name => worldLandmarks[name])
      .filter(point => point && point.score > 0.5);

    if (validPoints.length === 0) {
      return { x: 0, y: 0, z: 0 };
    }

    const sum = validPoints.reduce(
      (acc, point) => ({
        x: acc.x + point.x,
        y: acc.y + point.y,
        z: acc.z + point.z
      }),
      { x: 0, y: 0, z: 0 }
    );

    return {
      x: sum.x / validPoints.length,
      y: sum.y / validPoints.length,
      z: sum.z / validPoints.length
    };
  }

  /**
   * Apply temporal smoothing filter to reduce jitter
   */
  private applySmoothingFilter(current: LivePoseData, previous: LivePoseData): void {
    const smoothing = this.config.smoothingFactor;
    
    // Smooth screen coordinates
    Object.keys(current.screenCoordinates).forEach(name => {
      if (previous.screenCoordinates[name]) {
        const curr = current.screenCoordinates[name];
        const prev = previous.screenCoordinates[name];
        
        curr.x = curr.x * (1 - smoothing) + prev.x * smoothing;
        curr.y = curr.y * (1 - smoothing) + prev.y * smoothing;
        curr.z = curr.z * (1 - smoothing) + prev.z * smoothing;
      }
    });

    // Smooth joint angles
    Object.keys(current.jointAngles).forEach(name => {
      if (previous.jointAngles[name] !== undefined) {
        current.jointAngles[name] = 
          current.jointAngles[name] * (1 - smoothing) + 
          previous.jointAngles[name] * smoothing;
      }
    });
  }

  /**
   * Get joint configuration for styling and rendering
   * Provides color and radius specifications for each joint type
   */
  getJointConfig(): { [key: string]: { color: string; radius: number } } {
    return {
      nose: { color: '#FF0000', radius: 3 },
      left_eye: { color: '#FF6600', radius: 2 },
      right_eye: { color: '#FF6600', radius: 2 },
      left_ear: { color: '#FF9900', radius: 2 },
      right_ear: { color: '#FF9900', radius: 2 },
      left_shoulder: { color: '#FFAA00', radius: 4 },
      right_shoulder: { color: '#FFAA00', radius: 4 },
      left_elbow: { color: '#FFCC00', radius: 3 },
      right_elbow: { color: '#FFCC00', radius: 3 },
      left_wrist: { color: '#FFEE00', radius: 3 },
      right_wrist: { color: '#FFEE00', radius: 3 },
      left_hip: { color: '#FF4444', radius: 4 },
      right_hip: { color: '#FF4444', radius: 4 },
      left_knee: { color: '#FFDD00', radius: 4 },
      right_knee: { color: '#FFDD00', radius: 4 },
      left_ankle: { color: '#00FF44', radius: 3 },
      right_ankle: { color: '#00FF44', radius: 3 },
      left_heel: { color: '#00CC33', radius: 2 },
      right_heel: { color: '#00CC33', radius: 2 },
      left_foot_index: { color: '#00AA22', radius: 2 },
      right_foot_index: { color: '#00AA22', radius: 2 }
    };
  }

  /**
   * Get skeletal connections for overlay rendering
   */
  getSkeletalConnections(): Array<{ from: string; to: string; color: string; thickness: number }> {
    const style = this.config.overlayStyle;

    if (style === 'minimal') {
      // Minimal - only essential connections
      return [
        { from: 'left_shoulder', to: 'right_shoulder', color: '#FFFFFF', thickness: 1 },
        { from: 'left_shoulder', to: 'left_hip', color: '#FFFFFF', thickness: 1 },
        { from: 'right_shoulder', to: 'right_hip', color: '#FFFFFF', thickness: 1 },
        { from: 'left_hip', to: 'right_hip', color: '#FFFFFF', thickness: 1 },
        { from: 'left_hip', to: 'left_knee', color: '#FFFFFF', thickness: 1 },
        { from: 'left_knee', to: 'left_ankle', color: '#FFFFFF', thickness: 1 },
        { from: 'right_hip', to: 'right_knee', color: '#FFFFFF', thickness: 1 },
        { from: 'right_knee', to: 'right_ankle', color: '#FFFFFF', thickness: 1 },
      ];
    }

    const baseConnections = [
      // Spine
      { from: 'nose', to: 'left_shoulder', color: '#0088FF', thickness: 2 },
      { from: 'nose', to: 'right_shoulder', color: '#0088FF', thickness: 2 },
      { from: 'left_shoulder', to: 'right_shoulder', color: '#0088FF', thickness: 3 },
      { from: 'left_shoulder', to: 'left_hip', color: '#0088FF', thickness: 3 },
      { from: 'right_shoulder', to: 'right_hip', color: '#0088FF', thickness: 3 },
      { from: 'left_hip', to: 'right_hip', color: '#0088FF', thickness: 3 },

      // Legs
      { from: 'left_hip', to: 'left_knee', color: '#00AA55', thickness: 3 },
      { from: 'left_knee', to: 'left_ankle', color: '#00AA55', thickness: 3 },
      { from: 'right_hip', to: 'right_knee', color: '#00AA55', thickness: 3 },
      { from: 'right_knee', to: 'right_ankle', color: '#00AA55', thickness: 3 },

      // Arms
      { from: 'left_shoulder', to: 'left_elbow', color: '#FF8800', thickness: 2 },
      { from: 'left_elbow', to: 'left_wrist', color: '#FF8800', thickness: 2 },
      { from: 'right_shoulder', to: 'right_elbow', color: '#FF8800', thickness: 2 },
      { from: 'right_elbow', to: 'right_wrist', color: '#FF8800', thickness: 2 }
    ];

    if (style === 'medical') {
      // Add detailed connections for medical view
      const medicalAdditions = [
        { from: 'left_ankle', to: 'left_heel', color: '#00AA55', thickness: 2 },
        { from: 'left_heel', to: 'left_foot_index', color: '#00AA55', thickness: 2 },
        { from: 'right_ankle', to: 'right_heel', color: '#00AA55', thickness: 2 },
        { from: 'right_heel', to: 'right_foot_index', color: '#00AA55', thickness: 2 },
      ];
      return [...baseConnections, ...medicalAdditions];
    }

    if (style === 'athletic') {
      // Brighter colors for athletic
      return baseConnections.map(conn => ({
        ...conn,
        color: this.getAthleticColor(conn.color)
      }));
    }

    return baseConnections;
  }

  private getAthleticColor(baseColor: string): string {
    const colorMap: { [key: string]: string } = {
      '#0088FF': '#00DDFF', // Brighter blue for spine
      '#00AA55': '#00FF88', // Brighter green for legs
      '#FF8800': '#FFAA00'  // Brighter orange for arms
    };
    return colorMap[baseColor] || baseColor;
  }

  /**
   * Clean up resources
   */
  dispose(): void {
    if (this.detector) {
      this.detector.dispose();
      this.detector = null;
    }
    this.isInitialized = false;
    this.previousPose = null;
    console.log('🧹 Live 3D Pose Analyzer disposed');
  }
}

/**
 * Utility function to create analyzer with default config
 */
export const createLive3DAnalyzer = async (config: Partial<Live3DConfig> = {}): Promise<Live3DPoseAnalyzer> => {
  const defaultConfig: Live3DConfig = {
    userHeightMeters: 1.78, // 5'10"
    overlayStyle: 'medical',
    confidenceThreshold: 0.2,
    smoothingFactor: 0.2
  };

  const analyzer = new Live3DPoseAnalyzer({ ...defaultConfig, ...config });
  await analyzer.initialize();
  return analyzer;
};