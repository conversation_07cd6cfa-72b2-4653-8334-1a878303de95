/**
 * Verification utilities for pose analysis system
 * Implements the verification plan from SURGICAL_ROOT_CAUSE_ANALYSIS.md
 */

import { enableDebugMode, isDebugMode } from './Live3DPoseAnalyzer';

/**
 * Step 1: Enable Debug Mode
 * Implements localStorage.setItem('pose-debug', 'true') and window.POSE_DEBUG
 */
export const enablePoseDebugMode = (): void => {
  enableDebugMode();
  console.log('🐛 Pose debug mode enabled via verification utilities');
};

/**
 * Step 2: Test Initialization
 * Implements the checkInitialization function from verification plan
 */
export const checkInitialization = (): void => {
  const analyzer = document.querySelector('[data-analyzer-ready]');
  const svg = document.querySelector('svg.pose-overlay');

  // Test selector specificity
  const allPoseOverlays = document.querySelectorAll('.pose-overlay');
  const svgPoseOverlays = document.querySelectorAll('svg.pose-overlay');

  const result = {
    analyzerReady: !!analyzer,
    svgPresent: !!svg,
    svgDimensions: svg ? `${(svg as SVGSVGElement).viewBox.baseVal.width}x${(svg as SVGSVGElement).viewBox.baseVal.height}` : 'N/A',
    analyzerReadyValue: analyzer?.getAttribute('data-analyzer-ready'),
    debugMode: isDebugMode(),
    selectorTest: {
      totalPoseOverlayElements: allPoseOverlays.length,
      svgPoseOverlayElements: svgPoseOverlays.length,
      selectorWorksCorrectly: svgPoseOverlays.length === 1 && svg?.tagName === 'svg'
    }
  };

  console.log('🔍 Initialization Check:', result);

  // Verification plan compatibility test
  if (result.selectorTest.selectorWorksCorrectly) {
    console.log('✅ SVG selector compatibility verified: document.querySelector("svg.pose-overlay") works correctly');
  } else {
    console.warn('⚠️ SVG selector issue detected:', result.selectorTest);
  }

  // Additional verification
  if (result.analyzerReady && result.svgPresent) {
    console.log('✅ System initialization verified successfully');
  } else {
    console.warn('⚠️ System initialization incomplete:', {
      missingAnalyzer: !result.analyzerReady,
      missingSVG: !result.svgPresent
    });
  }

  return result;
};

/**
 * Step 3: Monitor Pose Detection
 * Implements pose detection monitor with document title updates
 */
export const setupPoseDetectionMonitor = (): void => {
  let poseCount = 0;
  const originalLog = console.log;
  
  console.log = function(...args) {
    // Monitor for pose detection messages
    if (args[0]?.includes && args[0].includes('✅ Pose detected')) {
      poseCount++;
      document.title = `Poses: ${poseCount}`;
      console.log(`📊 Pose detection count updated: ${poseCount}`);
    }
    
    // Call original console.log
    originalLog.apply(console, args);
  };
  
  console.log('📈 Pose detection monitor activated');
  console.log('📋 Document title will update with pose count');
};

/**
 * Step 4: Verify Coordinate Transform
 * Monitor coordinate transformation logs
 */
export const monitorCoordinateTransform = (): void => {
  const originalLog = console.log;
  
  console.log = function(...args) {
    // Monitor coordinate transformation logs
    if (args[0]?.includes && (
      args[0].includes('🔄 Transforming') ||
      args[0].includes('📐 Video dimensions') ||
      args[0].includes('📏 Real world scale') ||
      args[0].includes('📍')
    )) {
      console.log('🎯 COORDINATE_TRANSFORM:', ...args);
    }
    
    // Call original console.log
    originalLog.apply(console, args);
  };
  
  console.log('🔍 Coordinate transformation monitor activated');
};

/**
 * Step 5: Test SVG Overlay
 * Verify SVG overlay positioning and rendering
 */
export const testSVGOverlay = (): void => {
  const svg = document.querySelector('svg.pose-overlay');
  
  if (!svg) {
    console.error('❌ SVG overlay not found');
    return;
  }
  
  const svgElement = svg as SVGSVGElement;
  const rect = svgElement.getBoundingClientRect();
  const viewBox = svgElement.viewBox.baseVal;
  
  const overlayInfo = {
    present: true,
    dimensions: `${rect.width}x${rect.height}`,
    viewBox: `${viewBox.width}x${viewBox.height}`,
    preserveAspectRatio: svgElement.getAttribute('preserveAspectRatio'),
    position: {
      top: rect.top,
      left: rect.left
    },
    poseElements: svgElement.querySelectorAll('.pose-overlay').length,
    connections: svgElement.querySelectorAll('.skeletal-connections line').length,
    joints: svgElement.querySelectorAll('.joint-markers circle').length
  };
  
  console.log('🎨 SVG Overlay Analysis:', overlayInfo);
  
  // Verify positioning
  if (overlayInfo.preserveAspectRatio === 'none') {
    console.log('✅ SVG preserveAspectRatio correctly set to "none"');
  } else {
    console.warn('⚠️ SVG preserveAspectRatio should be "none" for direct pixel mapping');
  }
  
  return overlayInfo;
};

/**
 * Complete verification suite
 * Runs all verification steps in sequence
 */
export const runCompleteVerification = (): void => {
  console.log('🚀 Starting complete verification suite...');
  
  // Step 1: Enable debug mode
  enablePoseDebugMode();
  
  // Step 2: Check initialization
  setTimeout(() => {
    checkInitialization();
  }, 100);
  
  // Step 3: Setup pose detection monitor
  setTimeout(() => {
    setupPoseDetectionMonitor();
  }, 200);
  
  // Step 4: Monitor coordinate transforms
  setTimeout(() => {
    monitorCoordinateTransform();
  }, 300);
  
  // Step 5: Test SVG overlay
  setTimeout(() => {
    testSVGOverlay();
  }, 400);
  
  console.log('✅ Complete verification suite activated');
  console.log('📋 Monitor console and document title for real-time feedback');
};

// Export for global access
declare global {
  interface Window {
    poseVerification?: {
      enableDebugMode: typeof enablePoseDebugMode;
      checkInitialization: typeof checkInitialization;
      setupPoseDetectionMonitor: typeof setupPoseDetectionMonitor;
      monitorCoordinateTransform: typeof monitorCoordinateTransform;
      testSVGOverlay: typeof testSVGOverlay;
      runCompleteVerification: typeof runCompleteVerification;
    };
  }
}

// Make verification utilities globally available
if (typeof window !== 'undefined') {
  window.poseVerification = {
    enableDebugMode: enablePoseDebugMode,
    checkInitialization,
    setupPoseDetectionMonitor,
    monitorCoordinateTransform,
    testSVGOverlay,
    runCompleteVerification
  };
}
