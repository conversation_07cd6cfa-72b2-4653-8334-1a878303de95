/**
 * SYSTEM VERIFICATION TESTS FOR 3D POSE ANALYSIS
 *
 * This module provides comprehensive verification tests for the 3D pose analysis system
 * as outlined in Phase 3 of the Updated_Implementation_Guide.md.
 *
 * Tests include:
 * 1. BlazePose 3D initialization and basic functionality
 * 2. Height calibration verification
 * 3. Video processing pipeline validation
 * 4. Browser compatibility checks
 * 5. Performance monitoring
 * 6. Real-time debug monitoring for SVG overlay and video status
 */

import { BlazePose3DProcessor, POSE_CONSTANTS, Pose3DData } from './BlazePose3DProcessor';
import { enableDebugMode, isDebugMode } from './Live3DPoseAnalyzer';

// Browser compatibility interface
export interface BrowserCompatibility {
  compatible: boolean;
  missing: string[];
  webglSupported: boolean;
  tensorflowSupported: boolean;
}

// Debug monitoring interface
export interface DebugMonitoringStatus {
  svgConnections: number;
  svgJoints: number;
  loadingOverlayVisible: boolean;
  loadingOverlayText: string | null;
  videoReady: number;
  videoPlaying: boolean;
  timestamp: string;
}

/**
 * REAL-TIME DEBUG MONITORING SYSTEM
 *
 * Provides comprehensive monitoring of the pose analysis pipeline
 * including SVG overlay rendering, video status, and system health.
 * Based on SURGICAL_COMPLETE_SOLUTION.md Fix #4 specification.
 */

// Global monitoring state
let monitoringInterval: NodeJS.Timeout | null = null;
let isMonitoringActive = false;

/**
 * Enable comprehensive debug mode with real-time monitoring
 * This implements the exact specification from SURGICAL_COMPLETE_SOLUTION.md lines 420-422
 */
export const enableComprehensiveDebugMode = (): void => {
  // Enable debug mode using existing infrastructure
  enableDebugMode();

  // Set additional debug flags
  window.POSE_DEBUG = true;
  localStorage.setItem('pose-debug', 'true');

  console.log('🐛 Comprehensive debug mode enabled with real-time monitoring');

  // Start real-time monitoring
  startDebugMonitoring();
};

/**
 * Monitor pose detection system status
 * Implements the exact monitorPoses function from SURGICAL_COMPLETE_SOLUTION.md lines 425-441
 */
export const monitorPoses = (): DebugMonitoringStatus => {
  const svg = document.querySelector('svg');
  const connections = svg?.querySelectorAll('line').length || 0;
  const joints = svg?.querySelectorAll('circle').length || 0;

  console.log(`📊 SVG Status - Connections: ${connections}, Joints: ${joints}`);

  // Check if overlay is blocking
  const overlay = document.querySelector('.absolute.inset-0.flex.items-center.justify-center');
  let loadingOverlayVisible = false;
  let loadingOverlayText: string | null = null;

  if (overlay) {
    loadingOverlayVisible = true;
    loadingOverlayText = overlay.textContent || 'Unknown overlay content';
    console.log('⚠️ Loading overlay still visible:', loadingOverlayText);
  }

  // Check video state
  const video = document.querySelector('video');
  const videoReady = video?.readyState || 0;
  const videoPlaying = video ? !video.paused : false;

  console.log(`📹 Video ready: ${videoReady}, Playing: ${videoPlaying}`);

  return {
    svgConnections: connections,
    svgJoints: joints,
    loadingOverlayVisible,
    loadingOverlayText,
    videoReady,
    videoPlaying,
    timestamp: new Date().toISOString()
  };
};

/**
 * Start automated debug monitoring with 1-second intervals
 * Implements the setInterval monitoring from SURGICAL_COMPLETE_SOLUTION.md line 443
 */
export const startDebugMonitoring = (): void => {
  if (isMonitoringActive) {
    console.log('⚠️ Debug monitoring already active');
    return;
  }

  console.log('🔄 Starting automated debug monitoring (1-second intervals)');

  monitoringInterval = setInterval(() => {
    if (isDebugMode()) {
      monitorPoses();
    }
  }, 1000);

  isMonitoringActive = true;
};

/**
 * Stop automated debug monitoring
 */
export const stopDebugMonitoring = (): void => {
  if (monitoringInterval) {
    clearInterval(monitoringInterval);
    monitoringInterval = null;
    isMonitoringActive = false;
    console.log('⏹️ Debug monitoring stopped');
  }
};

/**
 * Get current monitoring status
 */
export const getMonitoringStatus = (): { active: boolean; debugMode: boolean } => {
  return {
    active: isMonitoringActive,
    debugMode: isDebugMode()
  };
};

/**
 * BROWSER CONSOLE UTILITIES
 *
 * These functions are exposed to the global window object for easy access
 * during development and testing via browser console.
 */

/**
 * Browser console helper: Enable debug monitoring
 * Usage in browser console: enablePoseDebug()
 */
export const enablePoseDebug = (): void => {
  enableComprehensiveDebugMode();
  console.log(`
🔧 POSE DEBUG MODE ENABLED

Available console commands:
- enablePoseDebug()     : Enable comprehensive debug mode
- disablePoseDebug()    : Disable debug monitoring
- checkPoseStatus()     : Get current system status
- monitorOnce()         : Run single monitoring check

Monitoring will run automatically every 1 second.
Check console for SVG status, video state, and overlay visibility.
  `);
};

/**
 * Browser console helper: Disable debug monitoring
 * Usage in browser console: disablePoseDebug()
 */
export const disablePoseDebug = (): void => {
  stopDebugMonitoring();
  localStorage.removeItem('pose-debug');
  window.POSE_DEBUG = false;
  console.log('🔇 Pose debug mode disabled');
};

/**
 * Browser console helper: Check current status
 * Usage in browser console: checkPoseStatus()
 */
export const checkPoseStatus = (): DebugMonitoringStatus => {
  console.log('📋 Current pose analysis system status:');
  const status = monitorPoses();
  console.table(status);
  return status;
};

/**
 * Browser console helper: Run single monitoring check
 * Usage in browser console: monitorOnce()
 */
export const monitorOnce = (): DebugMonitoringStatus => {
  return monitorPoses();
};

/**
 * Expose debug utilities to global window object for browser console access
 * This makes the debug functions easily accessible during development
 */
if (typeof window !== 'undefined') {
  (window as any).enablePoseDebug = enablePoseDebug;
  (window as any).disablePoseDebug = disablePoseDebug;
  (window as any).checkPoseStatus = checkPoseStatus;
  (window as any).monitorOnce = monitorOnce;
  (window as any).getMonitoringStatus = getMonitoringStatus;

  // Also expose the core monitoring function for direct access
  (window as any).monitorPoses = monitorPoses;
}

// Test result interface
export interface TestResult {
  success: boolean;
  message: string;
  details?: any;
  duration?: number;
}

/**
 * Test 1: BlazePose 3D Initialization Test
 * 
 * Verifies that BlazePose 3D can be properly initialized, tested, and disposed.
 * This is the core verification test for the 3D pose analysis system.
 */
export const testBlazePose3D = async (): Promise<boolean> => {
  const startTime = Date.now();
  
  try {
    console.log('🧪 Starting BlazePose 3D initialization test...');
    
    // Step 1: Create and initialize processor
    console.log('📝 Step 1: Creating BlazePose 3D processor...');
    const processor = new BlazePose3DProcessor();
    
    console.log('📝 Step 2: Initializing BlazePose 3D...');
    await processor.initialize();
    console.log('✅ BlazePose 3D initialized successfully');
    
    // Step 2: Test 3D detection capability with mock video element
    console.log('📝 Step 3: Testing 3D pose extraction capability...');
    const video = document.createElement('video');
    
    // Set minimal video properties for testing
    video.width = 640;
    video.height = 480;
    video.currentTime = 0;
    
    // Note: This will likely return null since there's no actual video content,
    // but it tests that the extraction method can be called without errors
    const result = await processor.extract3DPose(video, 0);
    console.log('✅ 3D extraction test completed (result may be null for mock video)');
    console.log(`📊 Extraction result: ${result ? 'Pose detected' : 'No pose (expected for mock video)'}`);
    
    // Step 3: Test processor disposal
    console.log('📝 Step 4: Testing processor disposal...');
    processor.dispose();
    console.log('✅ BlazePose 3D processor disposed successfully');
    
    const duration = Date.now() - startTime;
    console.log(`🎯 BlazePose 3D test completed successfully in ${duration}ms`);
    
    return true;
    
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error('❌ BlazePose 3D test failed:', error);
    console.error(`⏱️ Test failed after ${duration}ms`);
    
    // Provide specific error guidance
    if (error instanceof Error) {
      if (error.message.includes('WebGL')) {
        console.error('💡 Suggestion: WebGL not supported. Use Chrome or Firefox with hardware acceleration enabled.');
      } else if (error.message.includes('TensorFlow')) {
        console.error('💡 Suggestion: TensorFlow.js failed to load. Check internet connection and browser compatibility.');
      } else if (error.message.includes('BlazePose')) {
        console.error('💡 Suggestion: BlazePose model failed to load. Ensure TensorFlow.js models are accessible.');
      }
    }
    
    return false;
  }
};

/**
 * Browser Compatibility Check
 * 
 * Verifies that the current browser supports all required features for 3D pose analysis.
 */
export const checkBrowserCompatibility = (): BrowserCompatibility => {
  const missing: string[] = [];
  let webglSupported = false;
  let tensorflowSupported = false;
  
  try {
    // Check WebGL support
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    webglSupported = !!gl;
    if (!webglSupported) {
      missing.push('WebGL');
    }
    
    // Check TensorFlow.js support (basic check)
    if (typeof window !== 'undefined') {
      // Check if we can access TensorFlow.js (will be loaded dynamically)
      tensorflowSupported = true; // Assume supported, will be tested during actual initialization
    } else {
      missing.push('Window object (not in browser environment)');
    }
    
    // Check other required features
    if (!document.createElement) {
      missing.push('Document.createElement');
    }
    
    if (!window.fetch) {
      missing.push('Fetch API');
    }
    
    if (!Promise) {
      missing.push('Promise support');
    }
    
    if (!Array.from) {
      missing.push('ES6 Array methods');
    }
    
  } catch (error) {
    console.error('Browser compatibility check failed:', error);
    missing.push('Unknown compatibility issue');
  }
  
  const compatible = missing.length === 0;
  
  console.log('🌐 Browser Compatibility Check:');
  console.log(`✅ WebGL: ${webglSupported ? 'Supported' : 'Not supported'}`);
  console.log(`✅ TensorFlow.js: ${tensorflowSupported ? 'Supported' : 'Not supported'}`);
  console.log(`✅ Overall: ${compatible ? 'Compatible' : 'Issues found'}`);
  
  if (!compatible) {
    console.warn('⚠️ Missing features:', missing);
  }
  
  return {
    compatible,
    missing,
    webglSupported,
    tensorflowSupported
  };
};

/**
 * Memory Usage Monitoring
 * 
 * Tracks memory usage during pose analysis operations.
 */
export const monitorMemory = (): void => {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    const usedMB = (memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
    const totalMB = (memory.totalJSHeapSize / 1024 / 1024).toFixed(2);
    const limitMB = (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2);
    
    console.log(`📊 Memory usage: ${usedMB}MB / ${totalMB}MB (limit: ${limitMB}MB)`);
    
    // Warn if memory usage is high
    const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
    if (usagePercent > 80) {
      console.warn(`⚠️ High memory usage: ${usagePercent.toFixed(1)}%`);
    }
  } else {
    console.log('📊 Memory monitoring not available in this browser');
  }
};

/**
 * Processing Speed Tracking
 * 
 * Tracks processing performance for pose analysis operations.
 */
export const trackProcessingSpeed = (frameNumber: number, startTime: number): void => {
  const elapsed = (Date.now() - startTime) / 1000;
  const fps = frameNumber / elapsed;
  
  console.log(`⚡ Processing speed: ${fps.toFixed(1)} FPS (${frameNumber} frames in ${elapsed.toFixed(1)}s)`);
  
  // Performance guidance
  if (fps < 1) {
    console.warn('⚠️ Slow processing detected. Consider reducing video resolution or frame rate.');
  } else if (fps > 10) {
    console.log('🚀 Excellent processing speed!');
  } else if (fps > 5) {
    console.log('✅ Good processing speed');
  } else {
    console.log('📝 Moderate processing speed');
  }
};

/**
 * Test 2: Height Calibration Verification
 *
 * Verifies that height scaling is working correctly with real-world measurements.
 * This function analyzes a Pose3DData object and displays calibration details.
 */
export const verifyHeightScaling = (pose3D: Pose3DData): void => {
  console.log('📏 Height calibration verification:');
  console.log(`User height: ${POSE_CONSTANTS.USER_HEIGHT_METERS}m (5'10")`);
  console.log(`Real-world scale: ${pose3D.realWorldScale}`);
  console.log(`Height calibrated: ${pose3D.heightCalibrated}`);

  // Should show real measurements in meters
  Object.entries(pose3D.worldLandmarks).forEach(([name, landmark]) => {
    console.log(`${name}: (${landmark.x.toFixed(3)}, ${landmark.y.toFixed(3)}, ${landmark.z.toFixed(3)})m`);
  });

  // Additional verification checks
  const landmarkCount = Object.keys(pose3D.worldLandmarks).length;
  console.log(`📊 Total landmarks detected: ${landmarkCount}`);

  // Verify scale factor is reasonable
  if (pose3D.realWorldScale < 0.5 || pose3D.realWorldScale > 2.0) {
    console.warn(`⚠️ Scale factor ${pose3D.realWorldScale} is outside normal range (0.5-2.0)`);
  } else {
    console.log(`✅ Scale factor ${pose3D.realWorldScale} is within normal range`);
  }

  // Verify height calibration status
  if (pose3D.heightCalibrated) {
    console.log('✅ Height calibration is active');
  } else {
    console.warn('⚠️ Height calibration is not active - using default scaling');
  }

  // Check for key landmarks that should be present for height calibration
  const keyLandmarks = ['nose', 'left_hip', 'right_hip'];
  const missingKeyLandmarks = keyLandmarks.filter(name => !pose3D.worldLandmarks[name]);

  if (missingKeyLandmarks.length === 0) {
    console.log('✅ All key landmarks for height calibration are present');
  } else {
    console.warn(`⚠️ Missing key landmarks for height calibration: ${missingKeyLandmarks.join(', ')}`);
  }

  console.log('📏 Height calibration verification completed');
};

/**
 * Complete System Validation
 *
 * Runs all verification tests in sequence.
 */
export const validateSystem = async (): Promise<TestResult> => {
  console.log('🧪 Starting complete system validation...');
  const startTime = Date.now();
  
  try {
    // 1. Check browser compatibility
    console.log('📝 Step 1: Checking browser compatibility...');
    const compat = checkBrowserCompatibility();
    if (!compat.compatible) {
      return {
        success: false,
        message: 'Browser compatibility issues detected',
        details: { missing: compat.missing },
        duration: Date.now() - startTime
      };
    }
    
    // 2. Test BlazePose 3D
    console.log('📝 Step 2: Testing BlazePose 3D...');
    const blazePoseOK = await testBlazePose3D();
    if (!blazePoseOK) {
      return {
        success: false,
        message: 'BlazePose 3D initialization failed',
        duration: Date.now() - startTime
      };
    }
    
    // 3. Check memory usage
    console.log('📝 Step 3: Monitoring memory usage...');
    monitorMemory();
    
    const duration = Date.now() - startTime;
    console.log(`🎉 System validation completed successfully in ${duration}ms`);
    console.log('✅ System is ready for 3D pose analysis!');
    
    return {
      success: true,
      message: 'All verification tests passed',
      duration
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error('❌ System validation failed:', error);
    
    return {
      success: false,
      message: `System validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error },
      duration
    };
  }
};

// Export constants for easy access
export const VERIFICATION_CONSTANTS = {
  MIN_PROCESSING_FPS: 1,
  GOOD_PROCESSING_FPS: 5,
  EXCELLENT_PROCESSING_FPS: 10,
  MAX_MEMORY_USAGE_PERCENT: 80,
  TEST_VIDEO_WIDTH: 640,
  TEST_VIDEO_HEIGHT: 480
};
