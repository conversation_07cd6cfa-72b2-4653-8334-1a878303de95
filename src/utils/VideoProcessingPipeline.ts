/**
 * 3D VIDEO PROCESSING PIPELINE WITH OVERLAY RENDERING
 * 
 * This creates the pre-processed video output with 3D skeletal overlays embedded.
 * The user uploads a video, this processes it frame-by-frame, and outputs a new video
 * with the skeletal overlay already rendered.
 */

import { BlazePose3DProcessor, Pose3DData, SkeletalOverlayData, ProcessedFrame } from './BlazePose3DProcessor';

export interface VideoProcessingConfig {
  inputVideoUrl: string;
  outputVideoName: string;
  userHeightMeters: number;
  targetFPS: number;
  processEveryNthFrame: number;
  overlayStyle: 'medical' | 'athletic' | 'minimal';
}

export interface ProcessingProgress {
  frameNumber: number;
  totalFrames: number;
  percentage: number;
  currentTimestamp: number;
  estimatedTimeRemaining: string;
  status: 'initializing' | 'processing' | 'rendering' | 'completed' | 'error';
  message: string;
}

export class VideoProcessingPipeline {
  private processor3D: BlazePose3DProcessor | null = null;
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private videoElement: HTMLVideoElement;
  private mediaRecorder: MediaRecorder | null = null;
  private recordedChunks: Blob[] = [];

  constructor() {
    // Create canvas for rendering
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d')!;
    
    // Create video element for processing
    this.videoElement = document.createElement('video');
    this.videoElement.crossOrigin = 'anonymous';
    this.videoElement.playsInline = true;
    this.videoElement.muted = true;

    console.log('🎬 Video Processing Pipeline initialized');
  }

  /**
   * MAIN PROCESSING FUNCTION
   * This is the core function that processes the video and creates the output
   */
  async processVideo(
    config: VideoProcessingConfig,
    onProgress?: (progress: ProcessingProgress) => void
  ): Promise<Blob> {
    console.log('🚀 Starting video processing with 3D skeletal overlay...');
    
    try {
      // Step 1: Initialize 3D processor
      onProgress?.({
        frameNumber: 0,
        totalFrames: 0,
        percentage: 0,
        currentTimestamp: 0,
        estimatedTimeRemaining: 'Calculating...',
        status: 'initializing',
        message: 'Initializing BlazePose 3D processor...'
      });

      this.processor3D = new BlazePose3DProcessor();
      await this.processor3D.initialize();

      // Step 2: Load video
      onProgress?.({
        frameNumber: 0,
        totalFrames: 0,
        percentage: 5,
        currentTimestamp: 0,
        estimatedTimeRemaining: 'Calculating...',
        status: 'initializing',
        message: 'Loading video for processing...'
      });

      await this.loadVideo(config.inputVideoUrl);
      
      // Step 3: Setup canvas and recording
      this.setupCanvas();
      await this.setupRecording(config);

      // Step 4: Process frames
      const processedFrames = await this.processAllFrames(config, onProgress);

      // Step 5: Generate output video
      onProgress?.({
        frameNumber: processedFrames.length,
        totalFrames: processedFrames.length,
        percentage: 95,
        currentTimestamp: this.videoElement.duration,
        estimatedTimeRemaining: '00:05',
        status: 'rendering',
        message: 'Finalizing video output...'
      });

      const outputBlob = await this.finalizeRecording();

      onProgress?.({
        frameNumber: processedFrames.length,
        totalFrames: processedFrames.length,
        percentage: 100,
        currentTimestamp: this.videoElement.duration,
        estimatedTimeRemaining: '00:00',
        status: 'completed',
        message: `Successfully processed ${processedFrames.length} frames`
      });

      console.log(`✅ Video processing completed - ${processedFrames.length} frames processed`);
      return outputBlob;

    } catch (error) {
      console.error('❌ Video processing failed:', error);
      onProgress?.({
        frameNumber: 0,
        totalFrames: 0,
        percentage: 0,
        currentTimestamp: 0,
        estimatedTimeRemaining: 'Failed',
        status: 'error',
        message: `Processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
      throw error;
    } finally {
      this.cleanup();
    }
  }

  /**
   * Load video for processing
   */
  private async loadVideo(videoUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.videoElement.onloadedmetadata = () => {
        console.log(`📹 Video loaded: ${this.videoElement.videoWidth}x${this.videoElement.videoHeight}, ${this.videoElement.duration.toFixed(2)}s`);
        resolve();
      };

      this.videoElement.onerror = () => {
        reject(new Error('Failed to load video'));
      };

      this.videoElement.src = videoUrl;
    });
  }

  /**
   * Setup canvas dimensions
   */
  private setupCanvas(): void {
    this.canvas.width = this.videoElement.videoWidth;
    this.canvas.height = this.videoElement.videoHeight;
    
    console.log(`🎨 Canvas setup: ${this.canvas.width}x${this.canvas.height}`);
  }

  /**
   * Setup video recording
   */
  private async setupRecording(config: VideoProcessingConfig): Promise<void> {
    const stream = this.canvas.captureStream(config.targetFPS);
    
    this.mediaRecorder = new MediaRecorder(stream, {
      mimeType: 'video/webm;codecs=vp9'
    });

    this.recordedChunks = [];
    
    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.recordedChunks.push(event.data);
      }
    };

    this.mediaRecorder.start();
    console.log('🔴 Recording started');
  }

  /**
   * Process all frames with 3D pose detection and overlay rendering
   */
  private async processAllFrames(
    config: VideoProcessingConfig,
    onProgress?: (progress: ProcessingProgress) => void
  ): Promise<ProcessedFrame[]> {
    const { targetFPS, processEveryNthFrame } = config;
    const videoDuration = this.videoElement.duration;
    const totalFrames = Math.floor(videoDuration * targetFPS);
    const processedFrames: ProcessedFrame[] = [];

    console.log(`🎯 Processing ${totalFrames} frames at ${targetFPS} FPS`);

    const startTime = Date.now();

    for (let frameNumber = 0; frameNumber < totalFrames; frameNumber += processEveryNthFrame) {
      const timestamp = (frameNumber / targetFPS);
      
      // Seek to frame
      this.videoElement.currentTime = timestamp;
      await this.waitForVideoSeek();

      // Extract 3D pose (CRITICAL: This is where we get the 3D data)
      const pose3D: Pose3DData | null = await this.processor3D!.extract3DPose(this.videoElement, timestamp);
      
      let overlayData: SkeletalOverlayData | null = null;
      if (pose3D) {
        // Generate skeletal overlay data
        overlayData = this.processor3D!.generateSkeletalOverlay(
          pose3D,
          this.canvas.width,
          this.canvas.height
        );
      }

      // Render frame with overlay
      await this.renderFrameWithOverlay(overlayData, config.overlayStyle);

      // Store processed frame data
      const processedFrame: ProcessedFrame = {
        frameNumber,
        timestamp,
        pose3D,
        overlayData
      };
      processedFrames.push(processedFrame);

      // Report progress
      const elapsedTime = Date.now() - startTime;
      const framesPerSecond = frameNumber / (elapsedTime / 1000);
      const remainingFrames = totalFrames - frameNumber;
      const estimatedTimeRemaining = framesPerSecond > 0 
        ? this.formatTime(remainingFrames / framesPerSecond)
        : 'Calculating...';

      onProgress?.({
        frameNumber,
        totalFrames,
        percentage: (frameNumber / totalFrames) * 90, // Leave 10% for finalization
        currentTimestamp: timestamp,
        estimatedTimeRemaining,
        status: 'processing',
        message: pose3D 
          ? `Processing 3D pose at ${timestamp.toFixed(2)}s` 
          : `No pose detected at ${timestamp.toFixed(2)}s`
      });

      // Log every 30 frames
      if (frameNumber % 30 === 0) {
        console.log(`🎬 Processed frame ${frameNumber}/${totalFrames} (${timestamp.toFixed(2)}s) - ${pose3D ? '3D detected' : 'no pose'}`);
      }
    }

    return processedFrames;
  }

  /**
   * Render frame with 3D skeletal overlay
   */
  private async renderFrameWithOverlay(
    overlayData: SkeletalOverlayData | null,
    style: 'medical' | 'athletic' | 'minimal'
  ): Promise<void> {
    // Clear canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // Draw video frame
    this.ctx.drawImage(this.videoElement, 0, 0, this.canvas.width, this.canvas.height);

    if (!overlayData) {
      // No pose detected - draw "No Pose Detected" indicator
      this.drawNoPoseIndicator();
      return;
    }

    // Draw 3D skeletal overlay based on style
    switch (style) {
      case 'medical':
        this.drawMedicalStyleOverlay(overlayData);
        break;
      case 'athletic':
        this.drawAthleticStyleOverlay(overlayData);
        break;
      case 'minimal':
        this.drawMinimalStyleOverlay(overlayData);
        break;
    }

    // Draw 3D information overlay
    this.draw3DInfoOverlay(overlayData);
  }

  /**
   * Draw medical-style skeletal overlay
   */
  private drawMedicalStyleOverlay(overlayData: SkeletalOverlayData): void {
    const { joints, connections, angles } = overlayData;

    // Draw connections first
    connections.forEach(connection => {
      const fromJoint = joints[connection.from];
      const toJoint = joints[connection.to];

      if (fromJoint && toJoint && fromJoint.confidence > 0.3 && toJoint.confidence > 0.3) {
        this.ctx.beginPath();
        this.ctx.moveTo(fromJoint.x, fromJoint.y);
        this.ctx.lineTo(toJoint.x, toJoint.y);
        this.ctx.strokeStyle = connection.color;
        this.ctx.lineWidth = connection.thickness;
        this.ctx.lineCap = 'round';
        this.ctx.stroke();
      }
    });

    // Draw joints
    Object.entries(joints).forEach(([name, joint]) => {
      if (joint.confidence > 0.3) {
        this.ctx.beginPath();
        this.ctx.arc(joint.x, joint.y, 6, 0, 2 * Math.PI);
        this.ctx.fillStyle = this.getJointColor(name, joint.confidence);
        this.ctx.fill();
        this.ctx.strokeStyle = 'white';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
      }
    });

    // Draw angle measurements
    this.drawAngleMeasurements(joints, angles);
  }

  /**
   * Draw athletic-style skeletal overlay
   */
  private drawAthleticStyleOverlay(overlayData: SkeletalOverlayData): void {
    const { joints, connections } = overlayData;

    // More vibrant colors for athletic style
    connections.forEach(connection => {
      const fromJoint = joints[connection.from];
      const toJoint = joints[connection.to];

      if (fromJoint && toJoint && fromJoint.confidence > 0.3 && toJoint.confidence > 0.3) {
        // Add glow effect
        this.ctx.shadowColor = connection.color;
        this.ctx.shadowBlur = 10;
        
        this.ctx.beginPath();
        this.ctx.moveTo(fromJoint.x, fromJoint.y);
        this.ctx.lineTo(toJoint.x, toJoint.y);
        this.ctx.strokeStyle = connection.color;
        this.ctx.lineWidth = connection.thickness + 1;
        this.ctx.lineCap = 'round';
        this.ctx.stroke();
        
        this.ctx.shadowBlur = 0;
      }
    });

    // Draw dynamic joints with pulsing effect
    Object.entries(joints).forEach(([name, joint]) => {
      if (joint.confidence > 0.3) {
        const pulseSize = 4 + Math.sin(Date.now() / 200) * 2;
        
        this.ctx.beginPath();
        this.ctx.arc(joint.x, joint.y, pulseSize, 0, 2 * Math.PI);
        this.ctx.fillStyle = this.getJointColor(name, joint.confidence);
        this.ctx.fill();
      }
    });
  }

  /**
   * Draw minimal-style skeletal overlay
   */
  private drawMinimalStyleOverlay(overlayData: SkeletalOverlayData): void {
    const { joints, connections } = overlayData;

    // Simple, clean lines
    this.ctx.globalAlpha = 0.8;
    
    connections.forEach(connection => {
      const fromJoint = joints[connection.from];
      const toJoint = joints[connection.to];

      if (fromJoint && toJoint && fromJoint.confidence > 0.5) {
        this.ctx.beginPath();
        this.ctx.moveTo(fromJoint.x, fromJoint.y);
        this.ctx.lineTo(toJoint.x, toJoint.y);
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
      }
    });

    // Small, subtle joints
    Object.entries(joints).forEach(([_name, joint]) => {
      if (joint.confidence > 0.5) {
        this.ctx.beginPath();
        this.ctx.arc(joint.x, joint.y, 3, 0, 2 * Math.PI);
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fill();
      }
    });

    this.ctx.globalAlpha = 1.0;
  }

  /**
   * Draw angle measurements
   */
  private drawAngleMeasurements(
    joints: { [key: string]: { x: number; y: number; z: number; confidence: number } },
    angles: { [key: string]: number }
  ): void {
    // Draw angle arcs and values
    Object.entries(angles).forEach(([angleName, angleValue]) => {
      let joint: { x: number; y: number } | null = null;
      let color = '#FF6600';

      // Map angle names to joint positions
      switch (angleName) {
        case 'leftKnee':
          joint = joints['left_knee'];
          break;
        case 'rightKnee':
          joint = joints['right_knee'];
          break;
        case 'leftElbow':
          joint = joints['left_elbow'];
          color = '#FF8800';
          break;
        case 'rightElbow':
          joint = joints['right_elbow'];
          color = '#FF8800';
          break;
      }

      if (joint && joint.x && joint.y) {
        // Draw angle arc
        this.ctx.beginPath();
        this.ctx.arc(joint.x, joint.y, 25, 0, (angleValue / 180) * Math.PI);
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 3;
        this.ctx.stroke();

        // Draw angle text
        this.ctx.fillStyle = color;
        this.ctx.font = 'bold 14px Arial';
        this.ctx.fillText(`${Math.round(angleValue)}°`, joint.x + 30, joint.y - 10);
      }
    });
  }

  /**
   * Draw 3D information overlay
   */
  private draw3DInfoOverlay(overlayData: SkeletalOverlayData): void {
    const { realWorldScale, centerOfMass } = overlayData;

    // Info panel
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(10, 10, 250, 80);

    this.ctx.fillStyle = '#00FF88';
    this.ctx.font = 'bold 14px monospace';
    this.ctx.fillText('3D POSE ANALYSIS', 20, 30);

    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.font = '12px monospace';
    this.ctx.fillText(`Scale: ${realWorldScale.toFixed(3)}m/unit`, 20, 50);
    this.ctx.fillText(`Height: ${(1.78).toFixed(2)}m (5'10")`, 20, 65);
    this.ctx.fillText(`CoM: (${centerOfMass.x.toFixed(2)}, ${centerOfMass.y.toFixed(2)}, ${centerOfMass.z.toFixed(2)})`, 20, 80);
  }

  /**
   * Draw no pose indicator
   */
  private drawNoPoseIndicator(): void {
    this.ctx.fillStyle = 'rgba(255, 0, 0, 0.7)';
    this.ctx.fillRect(10, this.canvas.height - 50, 200, 30);

    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.font = 'bold 14px Arial';
    this.ctx.fillText('NO POSE DETECTED', 20, this.canvas.height - 30);
  }

  /**
   * Get joint color based on name and confidence
   */
  private getJointColor(name: string, confidence: number): string {
    const alpha = Math.max(0.5, confidence);
    
    if (name.includes('hip')) return `rgba(255, 68, 68, ${alpha})`;
    if (name.includes('knee')) return `rgba(255, 221, 0, ${alpha})`;
    if (name.includes('ankle') || name.includes('foot')) return `rgba(0, 255, 68, ${alpha})`;
    if (name.includes('shoulder')) return `rgba(255, 136, 0, ${alpha})`;
    if (name.includes('elbow')) return `rgba(255, 170, 0, ${alpha})`;
    if (name.includes('wrist')) return `rgba(255, 204, 0, ${alpha})`;
    
    return `rgba(136, 136, 255, ${alpha})`;
  }

  /**
   * Wait for video to seek to specific time
   */
  private waitForVideoSeek(): Promise<void> {
    return new Promise((resolve) => {
      const onSeeked = () => {
        this.videoElement.removeEventListener('seeked', onSeeked);
        resolve();
      };
      
      if (this.videoElement.readyState >= 2) {
        this.videoElement.addEventListener('seeked', onSeeked);
      } else {
        // If video isn't ready, wait a bit
        setTimeout(resolve, 50);
      }
    });
  }

  /**
   * Finalize recording and return video blob
   */
  private async finalizeRecording(): Promise<Blob> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder) {
        reject(new Error('Media recorder not initialized'));
        return;
      }

      this.mediaRecorder.onstop = () => {
        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        console.log(`✅ Video recording completed - ${(blob.size / 1024 / 1024).toFixed(2)}MB`);
        resolve(blob);
      };

      this.mediaRecorder.stop();
    });
  }

  /**
   * Format time in MM:SS format
   */
  private formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * Clean up resources
   */
  private cleanup(): void {
    if (this.processor3D) {
      this.processor3D.dispose();
      this.processor3D = null;
    }

    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }

    this.recordedChunks = [];
    console.log('🧹 Video processing pipeline cleaned up');
  }
}

// Helper function to create and run the processing pipeline
export const processVideoWithOverlay = async (
  config: VideoProcessingConfig,
  onProgress?: (progress: ProcessingProgress) => void
): Promise<Blob> => {
  const pipeline = new VideoProcessingPipeline();
  return await pipeline.processVideo(config, onProgress);
};