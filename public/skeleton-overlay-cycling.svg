<svg width="400" height="400" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
  <!-- Skeletal structure for cycling -->
  <g stroke="#35AE7C" stroke-width="3" fill="none">
    <!-- Head to shoulder -->
    <line x1="120" y1="100" x2="140" y2="120" />
    
    <!-- Torso -->
    <line x1="140" y1="120" x2="180" y2="170" />
    
    <!-- Arms to handlebar -->
    <line x1="140" y1="120" x2="80" y2="150" />
    
    <!-- Hip to knee -->
    <line x1="180" y1="170" x2="210" y2="220" />
    
    <!-- Knee to ankle -->
    <line x1="210" y1="220" x2="250" y2="270" />
    
    <!-- Foot -->
    <line x1="250" y1="270" x2="270" y2="270" />
    
    <!-- Bicycle frame (simplified) -->
    <line x1="80" y1="150" x2="180" y2="170" stroke="#999" stroke-width="2" />
    <line x1="180" y1="170" x2="270" y2="270" stroke="#999" stroke-width="2" />
    <line x1="270" y1="270" x2="160" y2="270" stroke="#999" stroke-width="2" />
    <line x1="160" y1="270" x2="80" y2="150" stroke="#999" stroke-width="2" />
    
    <!-- Wheels (simplified) -->
    <circle cx="160" cy="270" r="40" stroke="#999" stroke-width="2" />
    <circle cx="270" cy="270" r="40" stroke="#999" stroke-width="2" />
  </g>
  
  <!-- Joints -->
  <g fill="#35AE7C">
    <circle cx="120" cy="100" r="5" /> <!-- Head -->
    <circle cx="140" cy="120" r="5" /> <!-- Shoulder -->
    <circle cx="80" cy="150" r="5" /> <!-- Hand/handlebar -->
    <circle cx="180" cy="170" r="5" /> <!-- Hip/seat -->
    <circle cx="210" cy="220" r="5" /> <!-- Knee -->
    <circle cx="250" cy="270" r="5" /> <!-- Ankle -->
    <circle cx="270" cy="270" r="5" /> <!-- Foot/pedal -->
  </g>
  
  <!-- Angle markers (examples) -->
  <g>
    <!-- Knee angle -->
    <g transform="translate(210, 220)">
      <path d="M0,0 A20,20 0 0,1 -18,8.7" stroke="#FF7846" stroke-width="2" fill="none" />
      <text x="-25" y="10" fill="#FF7846" font-size="12" font-weight="bold">142°</text>
    </g>
    
    <!-- Hip angle -->
    <g transform="translate(180, 170)">
      <path d="M0,0 A20,20 0 0,1 -18.8,-6.8" stroke="#FF7846" stroke-width="2" fill="none" />
      <text x="-25" y="-10" fill="#FF7846" font-size="12" font-weight="bold">75°</text>
    </g>
    
    <!-- Torso angle -->
    <g transform="translate(140, 120)">
      <path d="M0,0 A20,20 0 0,1 17.3,10" stroke="#FF7846" stroke-width="2" fill="none" />
      <text x="15" y="10" fill="#FF7846" font-size="12" font-weight="bold">42°</text>
    </g>
  </g>
</svg>